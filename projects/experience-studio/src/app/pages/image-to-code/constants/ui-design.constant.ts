import { Buttons, IconOption } from '../models/image-to-code.model';

export const uiDesignConstants = {
  // Animated texts specific to UI Design generation
  animatedTexts: [
    'a modern landing page for..',
    'a mobile app interface for..',
    'a dashboard design with..',
    'a user profile page for..',
    'a product showcase page for..',
    'a contact form design for..',
    'a navigation menu for..',
    'a card-based layout for..',
    'a responsive design for..',
    'a minimalist interface for..',
  ],

  // Animated texts to show when an image is uploaded for UI Design
  imageUploadAnimatedTexts: [
    'a UI design based on this image..',
    'an interface that matches this style..',
    'a design system from this reference..',
    'a layout inspired by this mockup..',
    'a responsive design from this wireframe..',
    'a modern interface based on this concept..',
    'a user-friendly design from this example..',
    'a clean layout matching this style..',
    'a professional interface from this reference..',
    'a pixel-perfect design based on this image..',
  ],

  // Hero section content for UI Design
  heroSection: {
    title: 'Design Create Launch!',
    description: '<PERSON><PERSON><PERSON><PERSON> transforms your ideas into stunning UI designs with AI precision.',
    subHeading: 'What UI design would you like to create today?',
  },
};

// Application target options (mobile/web) for UI Design
// Note: Mobile is the default option (first in array)
export const applicationTargetOptions: IconOption[] = [
  {
    name: 'Mobile',
    icon: '/assets/icons/awe_mobile.svg', // Beautiful gradient mobile phone icon
    value: 'mobile',
    isLocalSvg: true,
  },
  {
    name: 'Web',
    icon: '/assets/icons/awe_web.svg', // Beautiful gradient desktop monitor icon
    value: 'web',
    isLocalSvg: true,
  },
];

// Dummy creative prompt options for UI Design demo/testing
const dummyUiDesignPrompts: Buttons[] = [
  {
    label: '✨Design a meditation app home screen with calming visuals',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create a pet adoption UI with card grid and filters',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Build a language learning dashboard with progress bars',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a virtual event landing page with schedule section',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create a personal journal UI with mood selector',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Build a quiz results page with confetti animation',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a travel itinerary UI with map and timeline',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create a music playlist UI with drag-and-drop',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Build a digital recipe book with tabs for categories',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a book club discussion UI with chat bubbles',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
];

// Utility to get N random unique items from an array
function getRandomUniqueItems<T>(arr: T[], n: number): T[] {
  const shuffled = arr.slice().sort(() => 0.5 - Math.random());
  return shuffled.slice(0, n);
}

// Export only 3 random dummy prompts for UI Design demo/testing
export const dummyUiDesignButtonLabels: Buttons[] = getRandomUniqueItems(dummyUiDesignPrompts, 3);

// Suggestion buttons specific to UI Design (random 3 from the full set)
const allUiDesignButtonLabels: Buttons[] = [
  {
    label: '✨Design a modern e-commerce product page with clean aesthetics',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create a dashboard interface for analytics with data visualization',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a mobile-first landing page for a SaaS product',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create a user profile page with avatar and bio',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a product showcase with carousel and reviews',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Build a contact form with map and social links',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create a card-based layout for blog posts',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a minimalist interface for a note-taking app',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Build a responsive navigation menu with dropdowns',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Create a professional portfolio grid for designers',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Design a settings page with toggles and sliders',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
  {
    label: '✨Build a pricing table with feature comparison',
    variant: 'secondary',
    loadingType: 'skeleton',
    buttonAnimation: 'pulse',
  },
];

export const uiDesignButtonLabels: Buttons[] = getRandomUniqueItems(allUiDesignButtonLabels, 3);

// Design style options for UI Design (replacing framework selection)
export const designStyleOptions: IconOption[] = [
  {
    name: 'Modern',
    icon: '/assets/icons/awe_tailwind.svg', // Using existing icon as placeholder
    value: 'modern',
    isLocalSvg: true,
  },
  {
    name: 'Minimalist',
    icon: '/assets/icons/awe_material.svg', // Using existing icon as placeholder
    value: 'minimalist',
    isLocalSvg: true,
  },
  {
    name: 'Corporate',
    icon: '/assets/icons/awe_bootstrap.svg', // Using existing icon as placeholder
    value: 'corporate',
    isLocalSvg: true,
  },
];

// Color scheme options for UI Design
export const colorSchemeOptions: IconOption[] = [
  {
    name: 'Light Theme',
    icon: '/assets/icons/awe_react.svg', // Using existing icon as placeholder
    value: 'light',
    isLocalSvg: true,
  },
  {
    name: 'Dark Theme',
    icon: '/assets/icons/awe_vue.svg', // Using existing icon as placeholder
    value: 'dark',
    isLocalSvg: true,
  },
  {
    name: 'Auto Theme',
    icon: '/assets/icons/awe_angular.svg', // Using existing icon as placeholder
    value: 'auto',
    isLocalSvg: true,
  },
];

// Export all constants for easy import
export const UI_DESIGN_CONSTANTS = {
  ...uiDesignConstants,
  applicationTargetOptions,
  uiDesignButtonLabels,
  designStyleOptions,
  colorSchemeOptions,
};

export default UI_DESIGN_CONSTANTS;

import {
  HttpRequest,
  HttpHandlerFn,
  HttpEvent,
  HttpErrorResponse,
  HttpInterceptorFn
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { inject } from '@angular/core';
import { GlobalErrorHandlerService } from '../services/error-handling/global-error-handler.service';
import { EnvironmentValidationService, EnvironmentValidationError } from '../services/environment-validation.service';
import { createLogger } from '../utils/logger';

/**
 * HTTP Error Interceptor
 * Centralized error handling for all HTTP requests
 * Replaces duplicated error handling code across services
 * ENHANCED: Excludes health check endpoints to prevent duplicate toast messages
 * ENHANCED: Validates environment.apiUrl before processing requests
 */
export const HttpErrorInterceptor: HttpInterceptorFn = (
  request: HttpRequest<any>,
  next: HttpHandlerFn
): Observable<HttpEvent<any>> => {
  const globalErrorHandler = inject(GlobalErrorHandlerService);
  const environmentValidation = inject(EnvironmentValidationService);
  const logger = createLogger('HttpErrorInterceptor');

  // ENHANCED: Validate environment URL before processing request
  try {
    environmentValidation.validateOrThrow();
  } catch (error) {
    if (error instanceof EnvironmentValidationError) {
      logger.error('❌ Environment validation failed, blocking HTTP request:', {
        url: request.url,
        method: request.method,
        error: error.message
      });

      // Return environment validation error as HTTP error
      return throwError(() => error);
    }

    // Re-throw unexpected errors
    throw error;
  }

  return next(request).pipe(
    catchError((error: HttpErrorResponse | EnvironmentValidationError) => {
      // Handle environment validation errors
      if (error instanceof EnvironmentValidationError) {
        logger.error('❌ Environment validation error during request:', error.message);
        return throwError(() => error);
      }

      // Handle HTTP errors
      const httpError = error as HttpErrorResponse;
      logger.error(`HTTP Error for ${request.method} ${request.url}:`, httpError);

      // ENHANCED: Skip global error handling for health check endpoints
      // Health check service handles its own error messages to prevent duplicates
      const isHealthCheckEndpoint = request.url.includes('/health');

      if (!isHealthCheckEndpoint) {
        // Handle the error through the global error handler
        globalErrorHandler.handleHttpError(httpError, request);
      } else {
        logger.info('Skipping global error handling for health check endpoint');
      }

      // Re-throw the error so services can still handle specific cases if needed
      return throwError(() => httpError);
    })
  );
};

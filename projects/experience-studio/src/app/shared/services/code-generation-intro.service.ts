import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, tap, switchMap, catchError, timeout } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { cacheHelpers } from '../interceptors/cache.interceptor';
import { createLogger } from '../utils/logger';
import { ToastService } from './toast.service';

// Interfaces for code generation intro
export interface CodeGenerationIntroRequest {
  code: CodeIntroItem[];
  user_request: string;
  message_type?: 'initial' | 'progress' | 'completion' | 'error' | 'polling_start' | 'polling_progress';
  context?: {
    stage?: string;
    progress?: string;
    error_details?: string;
    file_count?: number;
    preserved_edits?: number;
    conflicts?: number;
  };
}

export interface CodeIntroItem {
  fileName: string;
  content: string;
}

export interface CodeGenerationIntroResponse {
  message?: string;
  text?: string;
  content?: string;
}

export type CodeGenerationIntroResponseType = string | CodeGenerationIntroResponse;

export interface CodeIntroMessageState {
  isLoading: boolean;
  text: string;
  isTyping: boolean;
  hasError: boolean;
  shouldReplaceText: boolean;
  targetMessageId?: string;
  introAPICompleted: boolean;
  mainAPIInProgress: boolean;
  showLoadingIndicator: boolean;
  loadingPhase: 'intro' | 'main' | 'completed' | 'error';
  messageType?: 'generation' | 'regeneration';
}

export interface CodeParallelAPIResult {
  introText: string;
  mainAPIResult: any;
  introSuccess: boolean;
  mainAPISuccess: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class CodeGenerationIntroService {
  private apiUrl = environment.apiUrl;
  private introEndpoint = '/wireframe-generation/intro';
  private logger = createLogger('CodeGenerationIntroService');

  // FIXED: Remove shared state - each intro message should be standalone
  // Individual message states for each intro call (keyed by unique message ID)
  private individualMessageStates = new Map<string, BehaviorSubject<CodeIntroMessageState>>();

  // Typewriter effect configuration
  private typingSpeed = 15; // milliseconds per character
  private typewriterTimeouts: NodeJS.Timeout[] = [];

  constructor(
    private http: HttpClient,
    private toastService: ToastService
  ) {}

  /**
   * Create a new standalone intro message with unique ID
   * FIXED: Each intro call creates its own isolated state
   */
  private createStandaloneIntroMessage(messageId: string): BehaviorSubject<CodeIntroMessageState> {
    const initialState: CodeIntroMessageState = {
      isLoading: false,
      text: '',
      isTyping: false,
      hasError: false,
      shouldReplaceText: false,
      introAPICompleted: false,
      mainAPIInProgress: false,
      showLoadingIndicator: false,
      loadingPhase: 'intro',
      messageType: 'generation'
    };

    const messageState = new BehaviorSubject<CodeIntroMessageState>(initialState);
    this.individualMessageStates.set(messageId, messageState);

    this.logger.info('🆕 Created standalone intro message state:', { messageId });
    return messageState;
  }

  /**
   * Get or create message state for a specific message ID
   */
  public getMessageState(messageId: string): Observable<CodeIntroMessageState> {
    if (!this.individualMessageStates.has(messageId)) {
      this.createStandaloneIntroMessage(messageId);
    }
    return this.individualMessageStates.get(messageId)!.asObservable();
  }

  /**
   * Generate unique message ID for each intro call
   */
  private generateUniqueMessageId(): string {
    return `code-intro-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Create a standalone intro card for code regeneration
   * FIXED: Creates isolated intro cards that don't interfere with each other
   */
  public createStandaloneIntroCard(
    userRequest: string,
    codeFiles: any[]
  ): Observable<{ messageId: string; introText: string }> {
    this.logger.info('🆕 Creating standalone intro card for code regeneration');

    // Generate unique message ID for this intro card
    const messageId = this.generateUniqueMessageId();

    // Convert code files to intro code items
    const codeItems: CodeIntroItem[] = this.convertCodeFilesToIntroItems(codeFiles);

    // Create intro API call
    const introAPICall = this.generateCodeIntroMessage(userRequest, codeItems);

    return introAPICall.pipe(
      map(introText => {
        const validatedIntroText = this.validateIntroText(introText);

        this.logger.info('✅ Standalone intro card created:', {
          messageId,
          textLength: validatedIntroText.length
        });

        return {
          messageId,
          introText: validatedIntroText
        };
      }),
      catchError(error => {
        this.logger.error('❌ Failed to create standalone intro card:', error);
        const fallbackText = this.getFallbackIntroMessage(error);

        return of({
          messageId,
          introText: fallbackText
        });
      })
    );
  }

  /**
   * Create a standalone intro card and add it to the chat window
   * FIXED: Public method for components to create isolated intro cards
   */
  public createAndDisplayStandaloneIntroCard(
    userRequest: string,
    codeFiles: any[],
    chatWindowComponent?: any
  ): Observable<string> {
    return this.createStandaloneIntroCard(userRequest, codeFiles).pipe(
      tap(result => {
        // If chat window component is provided, add the message
        if (chatWindowComponent && chatWindowComponent.addStandaloneAIMessage) {
          chatWindowComponent.addStandaloneAIMessage(result.introText, result.messageId);
          this.logger.info('✅ Standalone intro card added to chat window:', result.messageId);
        }
      }),
      map(result => result.messageId)
    );
  }

  /**
   * Create a standalone intro card with shimmer loading and add it to the chat window
   * Shows shimmer until API response arrives - ONE-SHOT API CALL (no status/progress)
   */
  public createAndDisplayIntroCardWithShimmer(
    userRequest: string,
    codeFiles: any[],
    chatWindowComponent?: any
  ): Observable<string> {
    // Generate unique message ID for this intro card
    const messageId = this.generateUniqueMessageId();

    // First, add the shimmer loading card
    if (chatWindowComponent && chatWindowComponent.addAIMessageWithShimmer) {
      chatWindowComponent.addAIMessageWithShimmer(messageId);
      this.logger.info('🔄 Added shimmer loading card to chat window:', messageId);
    }

    // Convert code files to intro code items
    const codeItems: CodeIntroItem[] = this.convertCodeFilesToIntroItems(codeFiles);

    // Create intro API call - ONE-SHOT, no status/progress tracking needed
    const introAPICall = this.generateCodeIntroMessage(userRequest, codeItems);

    return introAPICall.pipe(
      tap(introText => {
        const validatedIntroText = this.validateIntroText(introText);

        // Update the card with the actual content and start fast typewriter effect
        if (chatWindowComponent && chatWindowComponent.updateAIMessageContentWithFastTypewriter) {
          chatWindowComponent.updateAIMessageContentWithFastTypewriter(messageId, validatedIntroText);
          this.logger.info('✅ Updated intro card with fast typewriter effect:', {
            messageId,
            textLength: validatedIntroText.length
          });
        } else if (chatWindowComponent && chatWindowComponent.updateAIMessageContent) {
          // Fallback to regular update if fast typewriter method doesn't exist
          chatWindowComponent.updateAIMessageContent(messageId, validatedIntroText);
          this.logger.info('✅ Updated intro card with API response:', {
            messageId,
            textLength: validatedIntroText.length
          });
        }
      }),
      map(() => messageId),
      catchError(error => {
        this.logger.error('❌ Failed to create intro card with shimmer:', error);
        const fallbackText = this.getFallbackIntroMessage(error);

        // Update the card with fallback content
        if (chatWindowComponent && chatWindowComponent.updateAIMessageContent) {
          chatWindowComponent.updateAIMessageContent(messageId, fallbackText);
        }

        return of(messageId);
      })
    );
  }

  /**
   * Update intro message state for a specific message ID
   * FIXED: Updates individual message state instead of shared state
   */
  private updateIndividualIntroState(messageId: string, newState: Partial<CodeIntroMessageState>): void {
    if (!this.individualMessageStates.has(messageId)) {
      this.createStandaloneIntroMessage(messageId);
    }

    const messageState = this.individualMessageStates.get(messageId)!;
    const currentState = messageState.getValue();
    const updatedState = { ...currentState, ...newState };
    messageState.next(updatedState);

    this.logger.debug('🎭 Individual intro state updated:', {
      messageId,
      isLoading: updatedState.isLoading,
      isTyping: updatedState.isTyping,
      hasError: updatedState.hasError,
      loadingPhase: updatedState.loadingPhase,
      textLength: updatedState.text.length
    });
  }

  /**
   * Start standalone typewriter effect for a specific message
   * FIXED: Creates isolated typewriter effect for individual messages
   */
  public startStandaloneTypewriterEffect(messageId: string, text: string): void {
    this.logger.info('⌨️ Starting standalone typewriter effect for message:', messageId);

    // Clear any existing timeouts
    this.clearTypewriterTimeouts();

    // Update individual message state to show typing
    this.updateIndividualIntroState(messageId, {
      isLoading: false,
      text: '',
      isTyping: true,
      hasError: false,
      shouldReplaceText: false,
      introAPICompleted: true,
      mainAPIInProgress: true,
      showLoadingIndicator: true,
      loadingPhase: 'main'
    });

    // Start typing character by character for this specific message
    this.typeCharacterForMessage(messageId, text, 0);
  }

  /**
   * Type a single character for a specific message with typewriter effect
   * FIXED: Isolated character typing for individual messages
   */
  private typeCharacterForMessage(messageId: string, fullText: string, charIndex: number): void {
    if (charIndex >= fullText.length) {
      // Typing complete for this message
      this.updateIndividualIntroState(messageId, {
        isLoading: true, // Keep loading for SSE events
        text: fullText,
        isTyping: false,
        hasError: false,
        shouldReplaceText: false,
        introAPICompleted: true,
        mainAPIInProgress: false,
        showLoadingIndicator: true,
        loadingPhase: 'main'
      });
      return;
    }

    // Update text with current progress for this specific message
    const currentText = fullText.substring(0, charIndex + 1);
    const currentState = this.individualMessageStates.get(messageId)?.getValue();
    if (currentState) {
      this.updateIndividualIntroState(messageId, {
        ...currentState,
        text: currentText,
        isTyping: true
      });
    }

    // Schedule next character
    const timeout = setTimeout(() => {
      this.typeCharacterForMessage(messageId, fullText, charIndex + 1);
    }, this.typingSpeed);

    this.typewriterTimeouts.push(timeout);
  }

  /**
   * Execute parallel API calls for code regeneration with intro message
   * FIXED: Creates standalone intro cards instead of using shared state
   * @param userRequest - The user's prompt
   * @param codeFiles - Array of current code files
   * @param mainAPICall - Observable for the main regeneration API
   * @param createNewCard - Whether to create a new standalone intro card
   * @returns Observable<CodeParallelAPIResult & { messageId: string }>
   */
  executeParallelRegeneration(
    userRequest: string,
    codeFiles: any[],
    mainAPICall: Observable<any>,
    createNewCard: boolean = true
  ): Observable<CodeParallelAPIResult & { messageId: string }> {
    this.logger.info('🔄 Starting parallel API calls for code regeneration with standalone intro message');
    this.logger.info('Code files count:', codeFiles.length);

    // FIXED: Generate unique message ID for this intro call
    const messageId = this.generateUniqueMessageId();

    // FIXED: Create standalone message state for this intro call
    const messageState = this.createStandaloneIntroMessage(messageId);

    // Initialize loading state for this specific intro message
    this.updateIndividualIntroState(messageId, {
      isLoading: true,
      text: '',
      isTyping: false,
      hasError: false,
      shouldReplaceText: false,
      introAPICompleted: false,
      mainAPIInProgress: true,
      showLoadingIndicator: true,
      loadingPhase: 'intro',
      messageType: 'regeneration'
    });

    // Convert code files to intro code items
    const codeItems: CodeIntroItem[] = this.convertCodeFilesToIntroItems(codeFiles);

    // Create intro API call with code file data
    const introAPICall = this.generateCodeIntroMessage(userRequest, codeItems);

    // Execute intro API first for regeneration, then handle main API separately
    return introAPICall.pipe(
      tap(introText => {
        this.logger.info('🎭 Code regeneration intro API completed successfully for message:', messageId);
        this.logger.info('🎭 Code regeneration intro text received:', {
          messageId,
          length: introText?.length || 0,
          preview: introText?.substring(0, 100) + (introText?.length > 100 ? '...' : ''),
          type: typeof introText
        });

        // Validate and sanitize intro text
        const validatedIntroText = this.validateIntroText(introText);

        // FIXED: Update individual message state instead of shared state
        this.updateIndividualIntroState(messageId, {
          isLoading: false,
          text: validatedIntroText,
          isTyping: false,
          hasError: false,
          shouldReplaceText: false,
          introAPICompleted: true,
          mainAPIInProgress: true,
          showLoadingIndicator: true,
          loadingPhase: 'main'
        });

        // FIXED: Start standalone typewriter effect for this specific message
        this.startStandaloneTypewriterEffect(messageId, validatedIntroText);
      }),
      switchMap(introText => {
        // Now execute main regeneration API and combine results
        return mainAPICall.pipe(
          map(mainAPIResult => ({
            introText,
            mainAPIResult,
            introSuccess: true,
            mainAPISuccess: true,
            messageId // FIXED: Include messageId in result
          })),
          tap(result => {
            this.logger.info('🎉 Main code regeneration API completed successfully for message:', messageId);

            // FIXED: Update individual message state instead of shared state
            // Keep intro message visible and maintain loading state for SSE
            this.updateIndividualIntroState(messageId, {
              isLoading: true, // Keep loading for SSE events
              text: this.validateIntroText(result.introText),
              isTyping: false,
              hasError: false,
              shouldReplaceText: false,
              introAPICompleted: true,
              mainAPIInProgress: false, // Main API done, but SSE still processing
              showLoadingIndicator: true, // Keep loading indicator for SSE
              loadingPhase: 'main' // Keep in main phase for SSE processing
            });

            this.logger.info('✅ Regeneration API completed for message:', messageId, '- intro message kept visible, waiting for SSE completion');
          })
        );
      }),
      catchError(error => this.handleIndividualParallelAPIError(error, messageId))
    );
  }

  /**
   * Call the intro API to get contextual messaging for code regeneration
   * @param userRequest - The user's prompt/request
   * @param codeItems - Array of code items for regeneration context
   * @param messageType - Type of message to generate
   * @param context - Additional context for the message
   * @returns Observable<string> - The intro text response
   */
  generateCodeIntroMessage(
    userRequest: string,
    codeItems: CodeIntroItem[] = [],
    messageType: 'initial' | 'progress' | 'completion' | 'error' | 'polling_start' | 'polling_progress' = 'initial',
    context?: any
  ): Observable<string> {
    this.logger.info('🎭 Calling code generation intro API for contextual messaging');
    this.logger.info('🎭 User request:', userRequest);
    this.logger.info('🎭 Code items count:', codeItems.length);

    // Validate input parameters
    if (!userRequest || userRequest.trim().length === 0) {
      this.logger.warn('🎭 Empty user request provided, using fallback');
      return of('Preparing your code regeneration...');
    }

    // Build and validate the request
    const request: CodeGenerationIntroRequest = {
      code: codeItems,
      user_request: userRequest.trim(),
      message_type: messageType,
      context: context
    };

    // Log the request payload for debugging
    this.logger.info('🎭 Code intro API request payload:', {
      user_request: request.user_request,
      code_items_count: request.code.length,
      code_items_preview: request.code.slice(0, 2).map(item => ({
        fileName: item.fileName,
        contentLength: item.content.length
      }))
    });

    // Disable caching for intro requests
    const cacheContext = cacheHelpers.disableCache();

    this.logger.info('🎭 Code intro API endpoint:', this.apiUrl + this.introEndpoint);

    return this.http.post<CodeGenerationIntroResponseType>(this.apiUrl + this.introEndpoint, request, {
      context: cacheContext,
      headers: {
        'Content-Type': 'application/json'
      }
    }).pipe(
      map(response => {
        this.logger.info('🎭 Code intro API response received:', response);

        // Handle both string and object response formats
        let introText: string = '';

        if (typeof response === 'string') {
          // Direct string response (new format)
          introText = response.trim();
          this.logger.info('🎭 Received direct string response from code intro API');
        } else if (response && typeof response === 'object') {
          // Object response (legacy format)
          introText = response.message || response.text || response.content || '';
          this.logger.info('🎭 Received object response from code intro API, extracted text from fields');
        } else {
          this.logger.warn('🎭 Unexpected response format from code intro API:', typeof response);
        }

        // Validate extracted text
        if (!introText || introText.trim().length === 0) {
          this.logger.warn('🎭 Code intro API response contains no text content');
          return 'Preparing your code regeneration...';
        }

        // Clean and validate the intro text
        const cleanedText = this.sanitizeIntroText(introText);
        this.logger.info('🎭 Successfully extracted and cleaned code intro text:', {
          originalLength: introText.length,
          cleanedLength: cleanedText.length,
          preview: cleanedText.substring(0, 100) + (cleanedText.length > 100 ? '...' : '')
        });

        return cleanedText;
      }),
      catchError(error => {
        this.logger.error('🎭 Code intro API call failed:', error);

        // Log detailed error information for debugging
        this.logger.error('🎭 Error details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.message,
          url: error.url
        });

        // Return fallback message instead of throwing error
        const fallbackMessage = this.getFallbackIntroMessage(error);
        this.logger.info('🎭 Using fallback code intro message:', fallbackMessage);

        return of(fallbackMessage);
      }),
      timeout(30000), // 30 second timeout for intro API
      catchError(timeoutError => {
        this.logger.error('🎭 Code intro API timeout after 30 seconds:', timeoutError);
        return of('Preparing your code regeneration...');
      })
    );
  }

  /**
   * Convert code files to intro code items
   * @param codeFiles - Array of code files
   * @returns Array of CodeIntroItem
   */
  private convertCodeFilesToIntroItems(codeFiles: any[]): CodeIntroItem[] {
    if (!codeFiles || !Array.isArray(codeFiles)) {
      this.logger.warn('🎭 Invalid code files provided for intro conversion');
      return [];
    }

    return codeFiles.map(file => ({
      fileName: file.path || file.fileName || file.name || 'Unknown file',
      content: file.code || file.content || ''
    })).filter(item => item.fileName && item.fileName !== 'Unknown file');
  }

  /**
   * Validate and sanitize intro text
   * @param text - Raw intro text
   * @returns Validated intro text
   */
  private validateIntroText(text: string): string {
    if (!text || typeof text !== 'string') {
      return 'Preparing your code regeneration...';
    }

    const cleanedText = this.sanitizeIntroText(text);
    return cleanedText.length > 0 ? cleanedText : 'Preparing your code regeneration...';
  }

  /**
   * Sanitize and clean intro text from API response
   * @param text - Raw intro text from API
   * @returns Cleaned and validated intro text
   */
  private sanitizeIntroText(text: string): string {
    if (!text || typeof text !== 'string') {
      return 'Preparing your code regeneration...';
    }

    // Remove any potential HTML tags or unwanted characters
    let cleanedText = text
      .trim()
      // Remove HTML tags if any
      .replace(/<[^>]*>/g, '')
      // Remove excessive whitespace
      .replace(/\s+/g, ' ')
      // Remove any control characters
      .replace(/[\x00-\x1F\x7F]/g, '')
      // Trim again after cleaning
      .trim();

    // Validate length (reasonable limits for intro text)
    if (cleanedText.length === 0) {
      this.logger.warn('🎭 Code intro text is empty after sanitization');
      return 'Preparing your code regeneration...';
    }

    if (cleanedText.length > 500) {
      this.logger.warn('🎭 Code intro text is very long, truncating');
      cleanedText = cleanedText.substring(0, 500) + '...';
    }

    return cleanedText;
  }

  /**
   * Get appropriate fallback intro message based on error type
   * @param error - The error that occurred
   * @returns Appropriate fallback message
   */
  private getFallbackIntroMessage(error: any): string {
    // Provide different fallback messages based on error type
    if (error.status === 0) {
      // Network error
      return 'Preparing your code regeneration...';
    } else if (error.status >= 500) {
      // Server error
      return 'Getting ready to regenerate your code...';
    } else if (error.status === 404) {
      // Not found
      return 'Setting up code regeneration...';
    } else if (error.status >= 400 && error.status < 500) {
      // Client error
      return 'Initializing code regeneration...';
    } else {
      // Unknown error
      return 'Preparing your code regeneration...';
    }
  }

  /**
   * Update intro message state (DEPRECATED - use updateIndividualIntroState instead)
   * @param newState - Partial state to update
   * @deprecated This method is deprecated. Use updateIndividualIntroState for specific messages.
   */
  private updateIntroState(newState: Partial<CodeIntroMessageState>): void {
    this.logger.warn('⚠️ updateIntroState is deprecated. Use updateIndividualIntroState instead.');
    // This method is now a no-op since we use individual message states
  }

  /**
   * Start typewriter effect for intro text (DEPRECATED)
   * @param text - The text to type
   * @deprecated Use startStandaloneTypewriterEffect instead
   */
  public startTypewriterEffect(text: string): void {
    this.logger.warn('⚠️ startTypewriterEffect is deprecated. Use startStandaloneTypewriterEffect instead.');
    // Generate a temporary message ID for backward compatibility
    const tempMessageId = this.generateUniqueMessageId();
    this.startStandaloneTypewriterEffect(tempMessageId, text);
  }

  /**
   * Start typewriter effect with loading indicator (DEPRECATED)
   * @param text - The text to type
   * @deprecated Use startStandaloneTypewriterEffect instead
   */
  public startTypewriterEffectWithLoading(text: string): void {
    this.logger.warn('⚠️ startTypewriterEffectWithLoading is deprecated. Use startStandaloneTypewriterEffect instead.');
    // Generate a temporary message ID for backward compatibility
    const tempMessageId = this.generateUniqueMessageId();
    this.startStandaloneTypewriterEffect(tempMessageId, text);
  }

  /**
   * Type a single character with typewriter effect (DEPRECATED)
   * @param fullText - The complete text to type
   * @param charIndex - Current character index
   * @deprecated Use typeCharacterForMessage instead
   */
  private typeCharacter(fullText: string, charIndex: number): void {
    this.logger.warn('⚠️ typeCharacter is deprecated. Use typeCharacterForMessage instead.');
    // This method is now a no-op since we use individual message states
  }

  /**
   * Type a single character with typewriter effect and loading indicator (DEPRECATED)
   * @param fullText - The complete text to type
   * @param charIndex - Current character index
   * @deprecated Use typeCharacterForMessage instead
   */
  private typeCharacterWithLoading(fullText: string, charIndex: number): void {
    this.logger.warn('⚠️ typeCharacterWithLoading is deprecated. Use typeCharacterForMessage instead.');
    // This method is now a no-op since we use individual message states
  }

  /**
   * Start text replacement with typewriter effect and loading indicator for a specific message (DEPRECATED)
   * @deprecated Use startStandaloneTypewriterEffect instead
   */
  public startTextReplacementWithLoading(text: string, targetMessageId: string): void {
    this.logger.warn('⚠️ startTextReplacementWithLoading is deprecated. Use startStandaloneTypewriterEffect instead.');
    // For backward compatibility, create a standalone typewriter effect
    this.startStandaloneTypewriterEffect(targetMessageId, text);
  }

  /**
   * Show fallback intro message when intro API fails (DEPRECATED)
   * @deprecated Use showFallbackIntroMessageForMessage instead
   */
  private showFallbackIntroMessage(): void {
    this.logger.warn('⚠️ showFallbackIntroMessage is deprecated. Use showFallbackIntroMessageForMessage instead.');
    // This method is now a no-op since we use individual message states
  }

  /**
   * Show error intro message when intro API fails (DEPRECATED)
   * @deprecated Use showErrorIntroMessageForMessage instead
   */
  private showErrorIntroMessage(): void {
    this.logger.warn('⚠️ showErrorIntroMessage is deprecated. Use showErrorIntroMessageForMessage instead.');
    // This method is now a no-op since we use individual message states
  }

  /**
   * Clear all typewriter timeouts
   */
  private clearTypewriterTimeouts(): void {
    this.typewriterTimeouts.forEach(timeout => clearTimeout(timeout));
    this.typewriterTimeouts = [];
  }

  /**
   * Complete text replacement and clear all loading states (DEPRECATED)
   * @deprecated Individual message states are managed automatically
   */
  public completeTextReplacement(): void {
    this.logger.warn('⚠️ completeTextReplacement is deprecated. Individual message states are managed automatically.');
    // This method is now a no-op since we use individual message states
  }

  /**
   * Complete regeneration process after SSE events are processed (DEPRECATED)
   * @deprecated Individual message states are managed automatically
   */
  public completeRegenerationAfterSSE(): void {
    this.logger.warn('⚠️ completeRegenerationAfterSSE is deprecated. Individual message states are managed automatically.');
    // This method is now a no-op since we use individual message states
  }

  /**
   * Reset the intro service state
   */
  public resetState(): void {
    this.logger.info('🔄 Resetting code intro service state');

    this.clearTypewriterTimeouts();

    // Clear all individual message states
    this.individualMessageStates.clear();

    this.logger.info('✅ All individual message states cleared');
  }

  /**
   * Generate progress message for code regeneration
   * @param userRequest - The user's request
   * @param codeFiles - Array of code files
   * @param stage - Current stage (e.g., 'building', 'deploying', 'analyzing')
   * @returns Observable<string>
   */
  generateProgressMessage(userRequest: string, codeFiles: any[], stage: string): Observable<string> {
    this.logger.info('🔄 Generating progress message for stage:', stage);

    const codeItems = this.convertCodeFilesToIntroItems(codeFiles);
    return this.generateCodeIntroMessage(userRequest, codeItems, 'progress', { stage }).pipe(
      catchError(error => {
        this.logger.error('❌ Progress message generation failed:', error);
        return of(this.getFallbackMessage('progress', { stage }));
      })
    );
  }

  /**
   * Generate completion message for code regeneration
   * @param userRequest - The user's request
   * @param codeFiles - Array of code files
   * @param results - Completion results (preserved edits, updated files, conflicts)
   * @returns Observable<string>
   */
  generateCompletionMessage(
    userRequest: string,
    codeFiles: any[],
    results: { preservedEdits: number; updatedFiles: number; conflicts: number }
  ): Observable<string> {
    this.logger.info('✅ Generating completion message with results:', results);

    const codeItems = this.convertCodeFilesToIntroItems(codeFiles);
    return this.generateCodeIntroMessage(userRequest, codeItems, 'completion', {
      preserved_edits: results.preservedEdits,
      file_count: results.updatedFiles,
      conflicts: results.conflicts
    }).pipe(
      catchError(error => {
        this.logger.error('❌ Completion message generation failed:', error);
        return of(this.getFallbackMessage('completion', {
          preserved_edits: results.preservedEdits,
          file_count: results.updatedFiles,
          conflicts: results.conflicts
        }));
      })
    );
  }

  /**
   * Generate error message for code regeneration
   * @param userRequest - The user's request
   * @param codeFiles - Array of code files
   * @param errorDetails - Error details
   * @returns Observable<string>
   */
  generateErrorMessage(userRequest: string, codeFiles: any[], errorDetails: string): Observable<string> {
    this.logger.error('❌ Generating error message for:', errorDetails);

    const codeItems = this.convertCodeFilesToIntroItems(codeFiles);
    return this.generateCodeIntroMessage(userRequest, codeItems, 'error', { error_details: errorDetails }).pipe(
      catchError(error => {
        this.logger.error('❌ Error message generation failed:', error);
        return of(this.getFallbackMessage('error', { error_details: errorDetails }));
      })
    );
  }

  /**
   * Generate polling start message for code regeneration
   * @param userRequest - The user's request
   * @param codeFiles - Array of code files
   * @returns Observable<string>
   */
  generatePollingStartMessage(userRequest: string, codeFiles: any[]): Observable<string> {
    this.logger.info('🔄 Generating polling start message');

    const codeItems = this.convertCodeFilesToIntroItems(codeFiles);
    return this.generateCodeIntroMessage(userRequest, codeItems, 'polling_start').pipe(
      catchError(error => {
        this.logger.error('❌ Polling start message generation failed:', error);
        return of(this.getFallbackMessage('polling_start'));
      })
    );
  }

  /**
   * Generate polling progress message for code regeneration
   * @param userRequest - The user's request
   * @param codeFiles - Array of code files
   * @param progress - Current progress description
   * @returns Observable<string>
   */
  generatePollingProgressMessage(userRequest: string, codeFiles: any[], progress: string): Observable<string> {
    this.logger.info('📊 Generating polling progress message for:', progress);

    const codeItems = this.convertCodeFilesToIntroItems(codeFiles);
    return this.generateCodeIntroMessage(userRequest, codeItems, 'polling_progress', { progress }).pipe(
      catchError(error => {
        this.logger.error('❌ Polling progress message generation failed:', error);
        return of(this.getFallbackMessage('polling_progress', { progress }));
      })
    );
  }

  /**
   * Get fallback message based on message type
   * @param messageType - Type of message
   * @param context - Additional context
   * @returns Fallback message string
   */
  getFallbackMessage(
    messageType: 'initial' | 'progress' | 'completion' | 'error' | 'polling_start' | 'polling_progress',
    context?: any
  ): string {
    // AUDIT CLEANUP: Removed hardcoded regeneration messages - now uses generic fallbacks
    switch (messageType) {
      case 'initial':
        return 'Processing your request...';
      case 'progress':
        return context?.stage ? `${context.stage}...` : 'Processing...';
      case 'completion':
        if (context?.preserved_edits !== undefined && context?.file_count !== undefined && context?.conflicts !== undefined) {
          return `Process completed! Preserved ${context.preserved_edits} user edits, updated ${context.file_count} files, ${context.conflicts} conflicts detected.`;
        }
        return 'Process completed successfully!';
      case 'error':
        return context?.error_details || 'Process failed. Please try again.';
      case 'polling_start':
        return 'Starting process...';
      case 'polling_progress':
        return context?.progress || 'Processing...';
      default:
        return 'Processing your request...';
    }
  }

  /**
   * Handle parallel API error scenarios
   * @param error - The error that occurred
   * @returns Observable with appropriate error handling
   */
  private handleParallelAPIError(error: any): Observable<CodeParallelAPIResult> {
    this.logger.error('💥 Code regeneration parallel API error:', error);

    // Check if error contains partial results
    const hasIntroText = error.introText && typeof error.introText === 'string';
    const hasMainAPIResult = error.mainAPIResult !== undefined && error.mainAPIResult !== null;

    if (hasIntroText && !hasMainAPIResult) {
      // Main API failed, intro succeeded
      this.logger.info('📝 Code intro API succeeded, main API failed');
      const validatedIntroText = this.validateIntroText(error.introText);
      this.startTypewriterEffect(validatedIntroText);

      return of({
        introText: validatedIntroText,
        mainAPIResult: null,
        introSuccess: true,
        mainAPISuccess: false
      });
    } else if (!hasIntroText && hasMainAPIResult) {
      // Intro API failed, main succeeded
      this.logger.info('🎯 Main API succeeded, code intro API failed');
      this.showFallbackIntroMessage();

      return of({
        introText: 'Preparing your code regeneration...',
        mainAPIResult: error.mainAPIResult,
        introSuccess: false,
        mainAPISuccess: true
      });
    } else {
      // Both failed or unknown error structure
      this.logger.error('💥 Both APIs failed or unknown error structure');
      this.logger.error('💥 Full error object:', error);

      // Show error state but don't re-throw to prevent breaking the main flow
      this.showErrorIntroMessage();

      return of({
        introText: 'Unable to prepare code regeneration context. Please try again.',
        mainAPIResult: null,
        introSuccess: false,
        mainAPISuccess: false
      });
    }
  }

  /**
   * Handle parallel API error scenarios for individual messages
   * FIXED: Handles errors for specific message IDs instead of shared state
   */
  private handleIndividualParallelAPIError(error: any, messageId: string): Observable<CodeParallelAPIResult & { messageId: string }> {
    this.logger.error('💥 Code regeneration parallel API error for message:', messageId, error);

    // Check if error contains partial results
    const hasIntroText = error.introText && typeof error.introText === 'string';
    const hasMainAPIResult = error.mainAPIResult !== undefined && error.mainAPIResult !== null;

    if (hasIntroText && !hasMainAPIResult) {
      // Main API failed, intro succeeded
      this.logger.info('📝 Code intro API succeeded, main API failed for message:', messageId);
      const validatedIntroText = this.validateIntroText(error.introText);
      this.startStandaloneTypewriterEffect(messageId, validatedIntroText);

      return of({
        introText: validatedIntroText,
        mainAPIResult: null,
        introSuccess: true,
        mainAPISuccess: false,
        messageId
      });
    } else if (!hasIntroText && hasMainAPIResult) {
      // Intro API failed, main succeeded
      this.logger.info('🎯 Main API succeeded, code intro API failed for message:', messageId);
      this.showFallbackIntroMessageForMessage(messageId);

      return of({
        introText: 'Preparing your code regeneration...',
        mainAPIResult: error.mainAPIResult,
        introSuccess: false,
        mainAPISuccess: true,
        messageId
      });
    } else {
      // Both failed or unknown error structure
      this.logger.error('💥 Both APIs failed or unknown error structure for message:', messageId);
      this.logger.error('💥 Full error object:', error);

      // Show error state for this specific message
      this.showErrorIntroMessageForMessage(messageId);

      return of({
        introText: 'Unable to prepare code regeneration context. Please try again.',
        mainAPIResult: null,
        introSuccess: false,
        mainAPISuccess: false,
        messageId
      });
    }
  }

  /**
   * Show fallback intro message for a specific message
   */
  private showFallbackIntroMessageForMessage(messageId: string): void {
    this.updateIndividualIntroState(messageId, {
      isLoading: false,
      text: 'Preparing your code regeneration...',
      isTyping: false,
      hasError: false,
      shouldReplaceText: false,
      introAPICompleted: false,
      mainAPIInProgress: false,
      showLoadingIndicator: false,
      loadingPhase: 'completed'
    });
  }

  /**
   * Show error intro message for a specific message
   */
  private showErrorIntroMessageForMessage(messageId: string): void {
    this.updateIndividualIntroState(messageId, {
      isLoading: false,
      text: 'Unable to prepare code regeneration context. Please try again.',
      isTyping: false,
      hasError: true,
      shouldReplaceText: false,
      introAPICompleted: false,
      mainAPIInProgress: false,
      showLoadingIndicator: false,
      loadingPhase: 'error'
    });
  }
}

import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, delay, catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { UserSignatureService } from './user-signature.service';
import { cacheHelpers } from '../interceptors/cache.interceptor';
import { FileModel } from '../components/code-viewer/code-viewer.component';


export interface CodeGenerationRequest {
  user_input: string;
  platform: string;
  framework: string;
  design_library: string;
  image: string[];
  project_id?: string;
  project_name?: string;
  project_description?: string;
  project_type?: string;
  project_state?: string;
}

export interface CodeGenerationResponse {
  code: string;
  status: string;
}

export interface CodeGenerationJobResponse {
  job_id: string;
  project_id:string;
}

export interface CodeGenerationStatusResponse {
  status: string;
  details: {
    status: string;
    log:string;
  };
}

export interface PreviewDeploymentResponse {
  deployedUrl: string;
  status: 'pending' | 'success' | 'failed';
}

export interface ElementModificationRequest {
  elementHtml: string;
  elementCss: string | null;
  elementPath: string;
  userMessage: string;
  projectId: string | null;
  jobId: string | null;
}

export interface ElementModificationResponse {
  modifiedHtml: string;
  status: 'success' | 'failed';
  message: string;
}

// Interface for code file in edit request
export interface CodeFileForEdit {
  fileName: string;
  content: string;
}

// Interface for code edit request payload
export interface CodeEditRequest {
  prompt: string; // Stringified JSON containing code and user_request
  image: string[]; // Image array - empty array if no images
}

// Interface for code edit response
export interface CodeEditResponse {
  files: CodeFileForEdit[];
  status: string;
  message?: string;
}



@Injectable({
  providedIn: 'root'
})
export class CodeGenerationService {
  private apiUrl = environment.apiUrl;
  private readonly baseUrl = 'YOUR_API_BASE_URL';
  endpoint = '/code-generation/generate/app';
  statusEndpoint = '/code-generation/status';
  projectTypeMapper: { [key: string]: string } = {
    'Generate UI Design': 'UI',
    'Generate Application': 'APP',
  };

  constructor(
    private http: HttpClient,
    private userSignatureService: UserSignatureService
  ) {}

  generateCode(request: CodeGenerationRequest, userSignature?: string): Observable<CodeGenerationJobResponse> {
    // If userSignature is not provided, use the UserSignatureService to get it
    const signature = userSignature || this.userSignatureService.getUserSignatureSync();

    // Add user_signature to query params only, not in the payload
    const params = new HttpParams().set('user_signature', signature);

    return this.http.post<CodeGenerationJobResponse>(this.apiUrl + this.endpoint, request, { params })
      .pipe(
        map((response: any) => {


          // Handle different response formats
          if (typeof response === 'string') {
            try {
              return JSON.parse(response);
            } catch (e) {
              return response;
            }
          } else if (response && typeof response === 'object') {

          }

          return response;
        })
      );
  }

  checkCodeGenerationStatus(projectId: string, statusId: string): Observable<CodeGenerationStatusResponse> {
    // Add query parameters for project_id and status_id
    let params = new HttpParams()
      .set('project_id', projectId)
      .set('status_id', statusId);

    // Status endpoints should always get fresh data, so disable caching
    const context = cacheHelpers.disableCache();

    return this.http.get<CodeGenerationStatusResponse>(`${this.apiUrl}${this.statusEndpoint}`, {
      params,
      context // Disable caching for status checks
    })
      .pipe(
        map((response: any) => {
          // Handle different response formats
          if (typeof response === 'string') {
            try {
              return JSON.parse(response);
            } catch (e) {
              return response;
            }
          }

          return response;
        })
      );
  }

  /**
   * Edit existing code based on user request
   * @param codeFiles Current code files from Monaco editor
   * @param userRequest User's edit request
   * @param images Optional images for context (can be empty array or undefined)
   * @param projectId Optional project ID for the regenerate API
   * @param userSignature Optional user signature
   * @returns Observable with the edit job response
   */
  editCode(
    codeFiles: FileModel[],
    userRequest: string,
    images?: string[],
    projectId?: string,
    jobId?: string,
    userSignature?: string
  ): Observable<CodeGenerationJobResponse> {
    const signature = userSignature || this.userSignatureService.getUserSignatureSync();
    const editEndpoint = '/code-generation/regenerate/code';

    // Add user_signature to query params
    let params = new HttpParams().set('user_signature', signature);

    // Add project_id to query params if provided
    if (projectId) {
      params = params.set('project_id', projectId);

    }

    // Add job_id to query params if provided
    if (jobId) {
      params = params.set('request_id', jobId);
    }

    // Create the prompt object that will be stringified
    const promptObject = {
      code: codeFiles.map(file => ({
        fileName: file.name || file.fileName || 'unknown.txt',
        content: file.content || ''
      })),
      user_request: userRequest
    };

    // Create the payload according to the specified format
    const payload: CodeEditRequest = {
      prompt: JSON.stringify(promptObject), // Stringify the prompt object
      image: images && images.length > 0 ? images : [] // Always include image array, empty if no images
    };

    return this.http.post<CodeGenerationJobResponse>(this.apiUrl + editEndpoint, payload, { params })
      .pipe(
        map((response: any) => {
          // Handle different response formats
          if (typeof response === 'string') {
            try {
              return JSON.parse(response);
            } catch (e) {
              return response;
            }
          }
          return response;
        }),
        catchError(error => {

          throw error;
        })
      );
  }

  getPreviewDeployment(generatedCode: string): Observable<PreviewDeploymentResponse> {
    const previewEndpoint = '/preview-deployment';

    // POST requests are not cached by default, but we're explicitly setting it for clarity
    const context = cacheHelpers.disableCache();

    return this.http.post<PreviewDeploymentResponse>(this.apiUrl + previewEndpoint, {
      code: generatedCode
    }, { context }).pipe(
      map(response => ({
        ...response,
        deployedUrl: response.deployedUrl || ''
      }))
    );
  }

  /**
   * Deploy a preview of the generated code
   * @param projectId Project ID
   * @param jobId Job ID
   * @param userSignature Optional user signature
   * @returns Observable with preview deployment response
   */
  deployPreview(projectId: string, jobId: string, userSignature?: string): Observable<PreviewDeploymentResponse> {
    const url = `${this.apiUrl}/code-generation/deploy`;

    // If userSignature is not provided, use the UserSignatureService to get it
    const signature = userSignature || this.userSignatureService.getUserSignatureSync();
    const params = new HttpParams()
      .set('user_signature', signature)
      .set('project_id', projectId)
      .set('job_id', jobId);

    // Use a short cache time (30 seconds) for deployment status
    // This helps reduce API load while still getting relatively fresh data
    const context = cacheHelpers.setMaxAge(30 * 1000); // 30 seconds

    return this.http.get<PreviewDeploymentResponse>(url, { params, context });
  }



  /**
   * Update design tokens for a project
   * @param updateRequest The design tokens update request
   * @returns Observable with the update response
   */
  updateDesignTokens(updateRequest: any): Observable<any> {
    const endpoint = '/code-generation/update-design-tokens';

    // Get user signature for the request
    const userSignature = this.userSignatureService.getUserSignatureSync();
    const params = new HttpParams().set('user_signature', userSignature);

    // POST requests are not cached by default, but we're explicitly setting it for clarity
    const context = cacheHelpers.disableCache();

    return this.http.post(`${this.apiUrl}${endpoint}`, updateRequest, { params, context })
      .pipe(
        map((response: any) => {
          // Handle different response formats
          if (typeof response === 'string') {
            try {
              return JSON.parse(response);
            } catch (e) {
              return response;
            }
          }
          return response;
        }),
        catchError(error => {

          return of({
            status: 'error',
            message: 'Failed to update design tokens',
            error: error
          });
        })
      );
  }

  /**
   * Sends a request to modify an element based on user input
   * @param request The element modification request
   * @returns An observable with the modified element
   */
  modifyElement(request: ElementModificationRequest): Observable<ElementModificationResponse> {
    const endpoint = '/code-generation/modify-element';

    // For now, we'll simulate a successful response since the API endpoint might not exist yet
    // In a real implementation, this would be an actual API call
    return of({
      modifiedHtml: this.simulateElementModification(request.elementHtml, request.userMessage),
      status: 'success' as 'success',
      message: 'Element modified successfully'
    }).pipe(
      delay(2000) // Simulate network delay
    );

    // Uncomment this when the API endpoint is ready
    /*
    return this.http.post<ElementModificationResponse>(this.apiUrl + endpoint, request)
      .pipe(
        map((response: any) => {


          // Handle different response formats
          if (typeof response === 'string') {
            try {

              return JSON.parse(response);
            } catch (e) {

              return response;
            }
          }

          return response;
        })
      );
    */
  }

  /**
   * Simulates element modification based on user message
   * This is a temporary implementation until the actual API endpoint is available
   * @param html The original HTML
   * @param userMessage The user's modification request
   * @returns The modified HTML
   */
  private simulateElementModification(html: string, userMessage: string): string {
    // Simple simulation of element modification based on common user requests
    let modifiedHtml = html;

    // Change text content
    if (userMessage.toLowerCase().includes('change text') ||
        userMessage.toLowerCase().includes('update text') ||
        userMessage.toLowerCase().includes('modify text')) {
      // Extract the text to change to from the user message
      const textMatch = userMessage.match(/to ["']([^"']+)["']/i);
      const newText = textMatch ? textMatch[1] : 'Updated Text';

      // Replace text content while preserving tags
      modifiedHtml = modifiedHtml.replace(/>([^<]+)</g, `>${newText}<`);
    }

    // Change color
    if (userMessage.toLowerCase().includes('color')) {
      // Extract color from the message
      const colorMatch = userMessage.match(/(red|blue|green|yellow|purple|orange|black|white|#[0-9a-f]{3,6})/i);
      const color = colorMatch ? colorMatch[1] : '#3366ff';

      // Add or update style attribute with color
      if (modifiedHtml.includes('style="')) {
        modifiedHtml = modifiedHtml.replace(/style="([^"]*)"/g, (match, styles) => {
          if (styles.includes('color:')) {
            return `style="${styles.replace(/color:[^;]+;?/, `color: ${color};`)}"`;
          } else {
            return `style="${styles}; color: ${color};"`;
          }
        });
      } else {
        modifiedHtml = modifiedHtml.replace(/<([a-z][a-z0-9]*)\s/i, `<$1 style="color: ${color};" `);
      }
    }

    // Add a class
    if (userMessage.toLowerCase().includes('add class') ||
        userMessage.toLowerCase().includes('apply class')) {
      // Extract class name from the message
      const classMatch = userMessage.match(/class\s+["']?([a-zA-Z0-9-_]+)["']?/i);
      const className = classMatch ? classMatch[1] : 'highlight';

      // Add the class
      if (modifiedHtml.includes('class="')) {
        modifiedHtml = modifiedHtml.replace(/class="([^"]*)"/g, `class="$1 ${className}"`);
      } else {
        modifiedHtml = modifiedHtml.replace(/<([a-z][a-z0-9]*)\s/i, `<$1 class="${className}" `);
      }
    }

    // Change font size
    if (userMessage.toLowerCase().includes('font size') ||
        userMessage.toLowerCase().includes('bigger') ||
        userMessage.toLowerCase().includes('larger') ||
        userMessage.toLowerCase().includes('smaller')) {
      // Extract size from the message
      const sizeMatch = userMessage.match(/(\d+)(px|em|rem|pt|%)/i);
      let fontSize = '1.2em';

      if (sizeMatch) {
        fontSize = `${sizeMatch[1]}${sizeMatch[2]}`;
      } else if (userMessage.toLowerCase().includes('bigger') ||
                userMessage.toLowerCase().includes('larger')) {
        fontSize = '1.5em';
      } else if (userMessage.toLowerCase().includes('smaller')) {
        fontSize = '0.8em';
      }

      // Add or update style attribute with font-size
      if (modifiedHtml.includes('style="')) {
        modifiedHtml = modifiedHtml.replace(/style="([^"]*)"/g, (match, styles) => {
          if (styles.includes('font-size:')) {
            return `style="${styles.replace(/font-size:[^;]+;?/, `font-size: ${fontSize};`)}"`;
          } else {
            return `style="${styles}; font-size: ${fontSize};"`;
          }
        });
      } else {
        modifiedHtml = modifiedHtml.replace(/<([a-z][a-z0-9]*)\s/i, `<$1 style="font-size: ${fontSize};" `);
      }
    }

    // If no specific modifications were made, add a generic modification
    if (modifiedHtml === html) {
      modifiedHtml = modifiedHtml.replace(/<([a-z][a-z0-9]*)\s/i, `<$1 data-modified="true" `);
    }

    return modifiedHtml;
  }

  // Helper method to map form selections to API request format
  mapSelectionsToRequest(
    prompt: string,
    formFactor: string | null,
    technology: string | null,
    designLibrary: string | null,
    imageDataUri: string | null,
    _userSignature?: string | null, // Kept for backward compatibility but not used in the returned object
    projectId?: string,
    cardTitle?: string // Added parameter for card title to determine project_type
  ): CodeGenerationRequest {
    // Map form factor to platform - ensure non-null string
    const platform = formFactor === 'mobile' ? 'mobile' : 'web';

    // Map technology to framework - ensure non-null string
    let framework = 'angular'; // Default
    if (technology === 'react') framework = 'react';
    if (technology === 'vue') framework = 'vue';

    // Map design library - ensure non-null string
    let designLib = 'custom';
    if (designLibrary === 'material-ui') designLib = 'materialui';
    if (designLibrary === 'tailwind') designLib = 'tailwindcss';
    if (designLibrary === 'bootstrap') designLib = 'bootstrap';

    // Prepare image array - ensure non-null array

    const imageArray = imageDataUri ? [imageDataUri] : [];

    // Determine project type based on card title using the same mapper as createProject
    const projectType = cardTitle
      ? (this.projectTypeMapper[cardTitle] || 'UI')
      : 'UI';

    // Generate dynamic project name from card title and prompt
    const projectName = cardTitle
      ? `${cardTitle} Project`
      : "Generated Project";

    // Ensure all values are non-null to match CodeGenerationRequest interface
    return {
      user_input: prompt || "I need to generate the code as close as possible for the input image",
      platform, // Already guaranteed to be 'mobile' or 'web'
      framework, // Already guaranteed to have a default value
      design_library: designLib, // Already guaranteed to have a default value
      image: imageArray,
      project_id: projectId,
      project_name: projectName, // Dynamic project name based on card title
      project_description: prompt || "Generated from image", // Use prompt as description
      project_type: projectType, // Use the mapped project type from card title
      project_state: "ACTIVE" // Default project state
    };
    // Note: user_signature is now passed separately to generateCode method
  }
}

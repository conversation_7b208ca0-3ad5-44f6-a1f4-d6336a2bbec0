import { Injectable, signal } from '@angular/core';
import { createLogger } from '../utils/logger';

export interface GenerationSelections {
  framework: string;
  designLibrary: string;
  applicationTarget: string;
}

export interface RepositoryMetadata {
  cloneUrl: string;
  repositoryName: string;
  repositoryUrl: string;
  branchName?: string;
  commitHash?: string;
}

export interface GenerationState {
  selections: GenerationSelections;
  repositoryMetadata: RepositoryMetadata | null;
  isTemplateLoading: boolean;
  isCodeTabEnabled: boolean;
}

/**
 * Service to manage shared generation state across components
 * Uses Angular 19+ Signals for reactive state management
 */
@Injectable({
  providedIn: 'root'
})
export class GenerationStateService {
  private readonly logger = createLogger('GenerationState');

  // Angular 19+ Signals for reactive state management
  public readonly generationState = signal<GenerationState>({
    selections: {
      framework: 'angular',
      designLibrary: 'tailwindcss',
      applicationTarget: 'web'
    },
    repositoryMetadata: null,
    isTemplateLoading: false,
    isCodeTabEnabled: false
  });

  constructor() {
    this.logger.info('🏗️ GenerationStateService initialized with Angular 19+ Signals');
  }

  /**
   * Update framework and design library selections
   */
  updateSelections(framework: string, designLibrary: string, applicationTarget: string = 'web'): void {
    const currentState = this.generationState();
    this.generationState.set({
      ...currentState,
      selections: {
        framework,
        designLibrary,
        applicationTarget
      }
    });

    this.logger.info('📋 Updated generation selections', { framework, designLibrary, applicationTarget });
  }

  /**
   * Update repository metadata from SSE events
   */
  updateRepositoryMetadata(metadata: RepositoryMetadata): void {
    const currentState = this.generationState();
    this.generationState.set({
      ...currentState,
      repositoryMetadata: metadata
    });

    this.logger.info('🔗 Updated repository metadata', { 
      cloneUrl: metadata.cloneUrl,
      repositoryName: metadata.repositoryName 
    });
  }

  /**
   * Update template loading state
   */
  updateTemplateLoadingState(isLoading: boolean): void {
    const currentState = this.generationState();
    this.generationState.set({
      ...currentState,
      isTemplateLoading: isLoading
    });

    this.logger.info('🏗️ Updated template loading state', { isLoading });
  }

  /**
   * Update code tab enabled state
   */
  updateCodeTabState(isEnabled: boolean): void {
    const currentState = this.generationState();
    this.generationState.set({
      ...currentState,
      isCodeTabEnabled: isEnabled
    });

    this.logger.info('📝 Updated code tab state', { isEnabled });
  }

  /**
   * Get current framework selection
   */
  getCurrentFramework(): string {
    return this.generationState().selections.framework;
  }

  /**
   * Get current design library selection
   */
  getCurrentDesignLibrary(): string {
    return this.generationState().selections.designLibrary;
  }

  /**
   * Get current repository metadata
   */
  getCurrentRepositoryMetadata(): RepositoryMetadata | null {
    return this.generationState().repositoryMetadata;
  }

  /**
   * Check if template is currently loading
   */
  isTemplateLoading(): boolean {
    return this.generationState().isTemplateLoading;
  }

  /**
   * Check if code tab is enabled
   */
  isCodeTabEnabled(): boolean {
    return this.generationState().isCodeTabEnabled;
  }

  /**
   * Reset all state to defaults
   */
  resetState(): void {
    this.generationState.set({
      selections: {
        framework: 'angular',
        designLibrary: 'tailwindcss',
        applicationTarget: 'web'
      },
      repositoryMetadata: null,
      isTemplateLoading: false,
      isCodeTabEnabled: false
    });

    this.logger.info('🔄 Generation state reset to defaults');
  }
}

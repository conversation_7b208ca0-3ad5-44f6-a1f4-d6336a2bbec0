import { Injectable, inject, signal } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { environment } from '../../../environments/environment';
import { createLogger } from '../utils/logger';
import { ToastService } from './toast.service';
import { FileModel } from '../components/code-viewer/code-viewer.component';
import JSZip from 'jszip';
import { DestroyRef } from '@angular/core';


export interface TemplateLoadingState {
  isLoading: boolean;
  hasError: boolean;
  errorMessage: string | null;
  progress: 'idle' | 'downloading' | 'processing' | 'completed';
  templateFiles: FileModel[];
}

@Injectable({
  providedIn: 'root'
})
export class TemplateLoadingService {
  private readonly logger = createLogger('TemplateLoading');
  private readonly http = inject(HttpClient);
  private readonly toastService = inject(ToastService);

  // Angular 19+ Signals for reactive state management
  public readonly templateLoadingState = signal<TemplateLoadingState>({
    isLoading: false,
    hasError: false,
    errorMessage: null,
    progress: 'idle',
    templateFiles: []
  });

  // BehaviorSubject for compatibility with existing patterns
  private readonly templateFilesSubject = new BehaviorSubject<FileModel[]>([]);
  public readonly templateFiles$ = this.templateFilesSubject.asObservable();

  // Track current template loading operation
  private currentLoadingOperation: AbortController | null = null;
  private destroyRef = inject(DestroyRef);
  constructor() {
    this.destroyRef = inject(DestroyRef);
    this.logger.info('🏗️ TemplateLoadingService initialized with Angular 19+ patterns');
  }

  /**
   * Load seed project template based on framework and design library
   * REFACTORED: Now uses single direct API call instead of two-step process
   * @param framework The selected framework (e.g., 'angular', 'react', 'vue')
   * @param designLibrary The selected design library (e.g., 'tailwindcss', 'bootstrap', 'material')
   * @returns Observable<FileModel[]> Template files ready for Monaco Editor
   */
  loadTemplate(framework: string, designLibrary: string): Observable<FileModel[]> {
    this.logger.info('🚀 Starting template loading process (single API call)', { framework, designLibrary });

    // Cancel any existing loading operation
    this.cancelCurrentOperation();

    // Create new abort controller for this operation
    this.currentLoadingOperation = new AbortController();

    // Update loading state
    this.updateLoadingState({
      isLoading: true,
      hasError: false,
      errorMessage: null,
      progress: 'downloading',
      templateFiles: []
    });

    return this.downloadTemplateDirectly(framework, designLibrary).pipe(
      tap(() => {
        this.updateLoadingState({ progress: 'processing' });
      }),
      switchMap(zipBlob => this.processZipFile(zipBlob)),
      tap(files => {
        this.updateLoadingState({
          isLoading: false,
          progress: 'completed',
          templateFiles: files
        });

        // ENHANCEMENT: Comprehensive debugging for template file loading
        this.logger.info('✅ Template loading completed successfully', {
          fileCount: files.length,
          files: files.map(f => ({
            name: f.name,
            fileName: f.fileName,
            contentLength: f.content?.length || 0,
            hasContent: !!f.content
          }))
        });

        // Emit files to subscribers (code-window component)
        this.templateFilesSubject.next(files);

        // Debug: Verify emission
        this.logger.debug('📡 Template files emitted to subscribers:', {
          subscriberCount: this.templateFilesSubject.observers.length,
          emittedFiles: files.length
        });

        this.toastService.success(`Template loaded successfully with ${files.length} files`);
      }),
      catchError(error => {
        this.handleTemplateLoadingError(error);
        return throwError(() => error);
      }),
      takeUntilDestroyed(this.destroyRef)
    );
  }

  /**
   * Download template ZIP file directly from the API
   * REFACTORED: Single API call instead of two-step process
   */
  private downloadTemplateDirectly(framework: string, designLibrary: string): Observable<Blob> {
    const params = new HttpParams()
      .set('framework', framework)
      .set('design_library', designLibrary);

    const url = `${environment.apiUrl}/download/template`;

    this.logger.info('⬇️ Downloading template ZIP file directly from API', { url, framework, designLibrary });

    return this.http.get(url, {
      params,
      responseType: 'blob'
    }).pipe(
      tap(blob => {
        this.logger.info('✅ Template ZIP downloaded directly', { size: blob.size });
      }),
      catchError(error => {
        this.logger.error('❌ Failed to download template directly', error);
        const errorMessage = 'Failed to download template. Please check your connection and try again.';
        this.toastService.error(errorMessage);
        return throwError(() => new Error(errorMessage));
      })
    );
  }

  /**
   * Process ZIP file and convert to FileModel array
   * Enhanced with better file filtering and Monaco Editor compatibility
   */
  private async processZipFile(zipBlob: Blob): Promise<FileModel[]> {
    try {
      this.logger.info('📦 Processing ZIP file', { size: zipBlob.size });

      const zip = new JSZip();
      const zipContent = await zip.loadAsync(zipBlob);
      const files: FileModel[] = [];

      // Process each file in the ZIP
      for (const [relativePath, zipEntry] of Object.entries(zipContent.files)) {
        // Enhanced filtering: Skip directories, hidden files, and system files
        if (this.shouldSkipFile(relativePath, zipEntry)) {
          continue;
        }

        try {
          const content = await zipEntry.async('string');
          const fileName = relativePath.split('/').pop() || relativePath;

          // Create FileModel compatible with Monaco Editor
          const fileModel: FileModel = {
            name: relativePath, // Full path for Monaco Editor
            type: 'file',
            content: content,
            fileName: fileName // Just the filename for display
          };

          files.push(fileModel);
          this.logger.debug('📄 Processed file', {
            path: relativePath,
            fileName: fileName,
            size: content.length
          });
        } catch (fileError) {
          this.logger.warn('⚠️ Failed to process file in ZIP', { path: relativePath, error: fileError });
        }
      }

      this.logger.info('✅ ZIP processing completed', {
        totalFiles: files.length,
        fileTypes: this.getFileTypesSummary(files),
        fileDetails: files.map(f => ({
          name: f.name,
          fileName: f.fileName,
          type: f.type,
          contentLength: f.content?.length || 0,
          contentPreview: f.content?.substring(0, 100) + '...'
        }))
      });

      // ENHANCEMENT: Validate all files have required properties
      const invalidFiles = files.filter(f => !f.name || !f.content);
      if (invalidFiles.length > 0) {
        this.logger.warn('⚠️ Found invalid files during ZIP processing:', invalidFiles);
      }

      return files;

    } catch (error) {
      this.logger.error('❌ Failed to process ZIP file', error);
      const errorMessage = 'Failed to process template files. Please try again.';
      this.toastService.error(errorMessage);
      throw new Error(errorMessage);
    }
  }

  /**
   * Enhanced file filtering logic
   */
  private shouldSkipFile(relativePath: string, zipEntry: any): boolean {
    // Skip directories
    if (zipEntry.dir) {
      return true;
    }

    // Skip hidden files and system files
    if (relativePath.startsWith('.') ||
        relativePath.includes('/.') ||
        relativePath.includes('__MACOSX') ||
        relativePath.includes('.DS_Store') ||
        relativePath.includes('Thumbs.db')) {
      return true;
    }

    // Skip empty paths
    if (!relativePath.trim()) {
      return true;
    }

    return false;
  }

  /**
   * Get summary of file types for logging
   */
  private getFileTypesSummary(files: FileModel[]): Record<string, number> {
    const summary: Record<string, number> = {};

    files.forEach(file => {
      const extension = file.fileName?.split('.').pop()?.toLowerCase() || 'unknown';
      summary[extension] = (summary[extension] || 0) + 1;
    });

    return summary;
  }

  /**
   * Update loading state using Angular Signals
   */
  private updateLoadingState(updates: Partial<TemplateLoadingState>): void {
    const currentState = this.templateLoadingState();
    this.templateLoadingState.set({ ...currentState, ...updates });
  }

  /**
   * Handle template loading errors
   */
  private handleTemplateLoadingError(error: any): void {
    this.logger.error('❌ Template loading failed', error);

    const errorMessage = error?.message || 'Failed to load template. Please try again.';

    this.updateLoadingState({
      isLoading: false,
      hasError: true,
      errorMessage: errorMessage,
      progress: 'idle'
    });

    // Show user-friendly error message
    // this.toastService.error(errorMessage);
  }

  /**
   * Cancel current loading operation
   */
  private cancelCurrentOperation(): void {
    if (this.currentLoadingOperation) {
      this.currentLoadingOperation.abort();
      this.currentLoadingOperation = null;
      this.logger.info('🚫 Cancelled previous template loading operation');
    }
  }

  /**
   * Reset template loading state
   */
  resetState(): void {
    this.cancelCurrentOperation();
    this.updateLoadingState({
      isLoading: false,
      hasError: false,
      errorMessage: null,
      progress: 'idle',
      templateFiles: []
    });
    this.templateFilesSubject.next([]);
    this.logger.info('🔄 Template loading state reset');
  }

  /**
   * Get current template files (for compatibility)
   */
  getCurrentTemplateFiles(): FileModel[] {
    return this.templateLoadingState().templateFiles;
  }

  /**
   * Check if template loading is in progress
   */
  isLoading(): boolean {
    return this.templateLoadingState().isLoading;
  }
}

<div class="chat-wrapper" [ngClass]="theme">
  <div class="chat-messages" #chatScrollContainer>
    @for (message of chatMessages; track message.id || $index) {
      @if (message.from === 'user') {
        <!-- User Message Card with Markdown Support -->
        <awe-cards
          variant="basic"
          size="small"
          class="user-card"
          [ngClass]="theme"
          [theme]="theme">
          <div content class="markdown-content">
            <!-- Display the image if it exists in the message -->
            @if (message.imageDataUri) {
              <div class="selected-image">
                <div
                  class="image-preview"
                  (click)="showImagePreview(message.imageDataUri, 'Selected image')">
                  <img [src]="message.imageDataUri" alt="Selected image" class="thumbnail-image" />
                </div>
              </div>
            }
            <markdown [data]="getSanitizedMessageText(message.text)"></markdown>
          </div>
        </awe-cards>
      } @else if (message.from === 'stepper' && message.isStepper) {
        <!-- Stepper Component integrated in chat flow -->
        <div class="stepper-container-inline" [ngClass]="theme">
          <app-vertical-stepper
            [theme]="theme"
            [progress]="progress"
            [progressDescription]="progressDescription"
            [status]="status"
            [restartable]="false"
            [projectId]="projectId"
            [jobId]="jobId"
            [useApi]="useApi"
            (stepUpdated)="onStepUpdated($event)"
            (retryStep)="onRetryStep($event)">
          </app-vertical-stepper>
        </div>
      } @else if (message.from === 'ai') {
        @if (message.isGenerationResult && message.generationResult) {
          <!-- Generation Result Card - Flows naturally in chat sequence -->
          <div class="ai-card generation-card" [ngClass]="theme">
            <app-generation-accordion
              [result]="message.generationResult"
              [theme]="theme"
              [defaultExpanded]="false"
              [projectId]="projectId"
              [jobId]="jobId">
            </app-generation-accordion>
          </div>
        } @else if (message.showLoadingIndicator === true && message.loadingPhase === 'intro') {
          <!-- SHIMMER LOADING: Show shimmer directly in chat container for intro/wireframe generation -->
          <div class="ai-shimmer-container" [ngClass]="theme">
            <app-intro-message-shimmer [theme]="theme"></app-intro-message-shimmer>
          </div>
        } @else {
          <!-- AI Message Card with Markdown Support -->
          <awe-cards
            variant="basic"
            size="small"
            class="ai-card"
            [ngClass]="theme"
            [theme]="theme">
            <div content class="markdown-content">
              <!-- Regular AI message content -->
              <div>
                <markdown [data]="getSanitizedMessageText(message.text)"></markdown>

                <!-- ENHANCED: Show intro message loading indicator for code generation -->
                @if (message.showIntroMessage && message.showLoadingIndicator && message.loadingPhase === 'intro') {
                  <div class="intro-loading-container">
                    <div class="intro-loading-dots">
                      <div class="dot"></div>
                      <div class="dot"></div>
                      <div class="dot"></div>
                    </div>
                    <!-- <span class="intro-loading-text">...</span> -->
                  </div>
                }

                <!-- ENHANCED: Show main API loading indicator for code generation -->
                @if (message.showLoadingIndicator && message.loadingPhase === 'main' && message.mainAPIInProgress) {
                  <div class="main-loading-container">
                    <div class="main-loading-dots">
                      <div class="dot"></div>
                      <div class="dot"></div>
                      <div class="dot"></div>
                    </div>
                    <!-- <span class="main-loading-text">...</span> -->
                  </div>
                }

                <!-- Show loading dots when stepper is active but not yet added to this message -->
                @if (showStepper && status !== 'COMPLETED' && !message.showLoadingIndicator) {
                  <div class="loading-dots">
                    <div class="dot"></div>
                    <div class="dot"></div>
                    <div class="dot"></div>
                  </div>
                }
              </div>
            </div>
          </awe-cards>
        }
      }
    } @empty {
      <div class="no-messages-placeholder d-flex flex-column align-items-center justify-content-center p-5">
        <div class="placeholder-icon mb-3">💬</div>
        <h3 class="placeholder-title mb-2">Start a Conversation</h3>
        <p class="placeholder-description text-center">
          Ask me anything about your project or describe what you'd like to build.
        </p>
      </div>
    }

    <!-- Stepper is now integrated into the main chat flow above -->

    <!-- UI Design Loading Indicator removed - now shown above prompt bar -->

    <!-- ENHANCEMENT: Generation Result Cards are now integrated into the main chat flow above -->

    <!-- Spacer to ensure content doesn't get hidden behind prompt bar -->
    <div class="prompt-spacer"></div>
  </div>

  <!-- ENHANCEMENT: Smart Scroll Toggle Button - FIXED outside scrollable area -->
  @if (scrollNavigationState().showScrollButton) {
    <div class="scroll-toggle-container-fixed" [ngClass]="theme">
      <button
        class="scroll-toggle-button"
        [ngClass]="theme"
        (click)="onScrollToggleClick()"
        [attr.aria-label]="getScrollButtonTooltip()"
        [title]="getScrollButtonTooltip()">
        <awe-icons
          [iconName]="getScrollButtonIcon()"
          [color]="theme === 'dark' ? '#ffffff' : '#333333'"
          [ngClass]="theme"
          class="scroll-icon">
        </awe-icons>
      </button>
    </div>
  }

  <!-- Generating text above prompt bar -->
  @if (shouldShowUIDesignLoadingIndicator()) {
    <div class="generating-indicator" [ngClass]="theme">
      <div class="generating-stepper">
        <!-- Exact same spinner from stepper component -->
        <div class="modern-loading-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-core"></div>
        </div>
        <span class="generating-text">Generating...</span>
      </div>
    </div>
  }

  <!-- Code Generation Loading Indicator -->
  @if (shouldShowCodeGenerationLoadingIndicator()) {
    <div class="generating-indicator" [ngClass]="theme">
      <div class="generating-stepper">
        <!-- Exact same spinner from stepper component -->
        <div class="modern-loading-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-core"></div>
        </div>
        <span class="generating-text">Generating...</span>
      </div>
    </div>
  }

  <!-- ENHANCED: Prompt Bar Loading Indicator -->
  @if (shouldShowPromptBarLoadingIndicator()) {
    <div class="regeneration-progress-indicator" [ngClass]="theme">
      <div class="regeneration-stepper">
        <!-- Exact same spinner from stepper component -->
        <div class="modern-loading-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-core"></div>
        </div>
        <span class="regeneration-text">{{ currentPromptBarLoadingState.loadingText }}</span>
      </div>
    </div>
  }



  <awe-prompt-bar
    [theme]="theme"
    [defaultText]="defaultText"
    [(textValue)]="textValue"
    (enterPressed)="canSendRequest() ? handleEnhancedSend() : null"
    [variant]="'chat-bot'"
    [class.disabled-prompt-bar]="!isCodeGenerationComplete"
    [class.processing]="isEnhancing || isCodeGenerationLoading">

    <!-- Custom content with exact same functionality as prompt-content -->
    <div class="custom-content">
      <!-- Selected Files Display (no preview functionality) -->
      <div class="mb-2 selected-files" *ngIf="selectedFiles.length > 0">
        <div class="py-1 px-2 file-item" *ngFor="let file of selectedFiles">
          <div class="file-info">
            <img
              class="mr-2 file-preview-image"
              [src]="file.url"
              [alt]="file.name"
              loading="lazy"
              decoding="async" />
            <span class="file-name">{{ truncateFileName(file.name) }}</span>
          </div>
          <awe-icons
            iconName="awe_close"
            (click)="removeFile(file.id)"
            class="pt-2"
            role="button"
            tabindex="0"
            iconColor="blue"
            [attr.aria-label]="'Remove ' + file.name"></awe-icons>
        </div>
      </div>

      <div class="d-flex align-items-center justify-content-between pe-2 tools-container">
        <div class="d-flex align-items-center gap-2 me-3 attach-container">
          <!-- Simple file attach icon (no pill) -->
          <awe-icons
            iconName="awe_enhanced_alternate"
            (click)="handleFileAttach()"
            role="button"
            tabindex="0"
            [attr.aria-label]="'Attach Image'"
            [class.disabled]="isFileAttachDisabled || isEnhancing || isCodeGenerationLoading"
            [style.cursor]="
              isFileAttachDisabled || isEnhancing || isCodeGenerationLoading
                ? 'not-allowed'
                : 'pointer'
            "
            [color]="getIconColor()"></awe-icons>
        </div>

        <div class="d-flex align-items-center gap-3 enhance-icons">
          <!-- Show loading spinner when enhancing, otherwise show enhance icon -->
          <ng-container *ngIf="!isEnhancing; else loadingEnhance">
            <awe-icons
              iconName="awe_enhance"
              (click)="handleEnhanceText()"
              role="button"
              tabindex="0"
              [attr.aria-label]="'Enhance'"
              [class.disabled]="
                !textValue ||
                textValue.trim() === '' ||
                enhanceClickCount >= maxEnhanceClicks ||
                isCodeGenerationLoading
              "
              [style.cursor]="
                !textValue ||
                textValue.trim() === '' ||
                enhanceClickCount >= maxEnhanceClicks ||
                isCodeGenerationLoading
                  ? 'not-allowed'
                  : 'pointer'
              "
              [color]="getIconColor()"></awe-icons>
          </ng-container>
          <ng-template #loadingEnhance>
            <div class="d-flex align-items-center justify-content-center loading-spinner">
              <div class="spinner"></div>
            </div>
          </ng-template>

          <awe-icons
            iconName="awe_enhanced_send"
            (click)="handleEnhancedSend()"
            class="cursor-pointer"
            role="button"
            tabindex="0"
            [attr.aria-label]="'Enhanced Send'"
            [class.disabled]="!canSendRequest()"
            [style.cursor]="!canSendRequest() ? 'not-allowed' : 'pointer'"
            [color]="getIconColor()"></awe-icons>
        </div>
      </div>
    </div>
  </awe-prompt-bar>
</div>

<!-- Image Preview Overlay -->
@if (showPreview && previewImage) {
  <div class="preview-overlay">
    <div class="preview-content">
      <div class="preview-header">
        <div class="preview-title">{{ previewImage.name }}</div>
        <awe-icons
          iconName="awe_close"
          (click)="closeImagePreview()"
          role="button"
          tabindex="0"
          [attr.aria-label]="'Close preview'"
          [color]="getIconColor()"></awe-icons>
      </div>
      <div class="preview-body">
        <img [src]="previewImage.url" [alt]="previewImage.name" />
      </div>
    </div>
  </div>
}

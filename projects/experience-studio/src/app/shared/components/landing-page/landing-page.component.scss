#main-content-container {
  margin-top: 7%;
  .studio-cards-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    .studio-card {
      border: 1px solid var(--code-viewer-border) !important;
      background-color: var(--code-viewer-bg) !important;
      &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        padding: 2px;
        background: linear-gradient(90deg, #8c65f7 0%, #e84393 100%);
        mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        mask-composite: exclude;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
        z-index: 1;
      }
      &:hover {
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        &::before {
          opacity: 1;
        }
      }
      img {
        width: auto;
        height: auto;
        object-fit: contain;
      }
    }
  }
  @media (max-width: 1024px) {
    .studio-cards-grid .studio-card {
      height: 260px;
      img {
        max-height: 120px;
      }
    }
  }
  @media (max-width: 767px) {
    .studio-cards-grid {
      grid-template-columns: 1fr;
      .studio-card {
        height: 240px;
        img {
          max-height: 100px;
        }
      }
    }
  }
  @media (max-width: 480px) {
    .studio-cards-grid .studio-card {
      height: 220px;
      img {
        max-height: 80px;
      }
    }
  }
  @media (min-width: 1420px) {
    .studio-cards-grid {
      max-width: 1200px;
    }
  }
}

/* ENHANCED: Health check info message styles */
.health-check-info-message {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 1.5rem;
  border-radius: 0.5rem;
  max-width: 600px;
  margin: 0 auto;
  border: 1px solid;
  background-color: var(--warning-bg, #fff3cd);
  border-color: var(--warning-border, #ffeaa7);
  color: var(--warning-text, #856404);

  .info-icon {
    margin-right: 0.75rem;
    font-size: 1.25rem;
    color: var(--warning-icon, #f39c12);

    i {
      display: block;
    }
  }

  .info-text {
    flex: 1;

    p {
      margin: 0;
      font-size: 0.95rem;
      font-weight: 500;
      line-height: 1.4;
    }
  }

  /* Light theme specific styles */
  &.light-theme {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;

    .info-icon {
      color: #f39c12;
    }
  }

  /* Dark theme specific styles */
  &.dark-theme {
    background-color: #2d2a1f;
    border-color: #4a4332;
    color: #ffd93d;

    .info-icon {
      color: #ffa502;
    }
  }

  /* Responsive design */
  @media (max-width: 768px) {
    padding: 0.75rem 1rem;
    margin: 0 1rem;

    .info-icon {
      margin-right: 0.5rem;
      font-size: 1.1rem;
    }

    .info-text p {
      font-size: 0.9rem;
    }
  }
}

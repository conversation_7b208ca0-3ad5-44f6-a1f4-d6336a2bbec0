#main-content-container {
  margin-top: 7%;
  .studio-cards-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    .studio-card {
      border: 1px solid var(--code-viewer-border) !important;
      background-color: var(--code-viewer-bg) !important;
      &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        padding: 2px;
        background: linear-gradient(90deg, #8c65f7 0%, #e84393 100%);
        mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        mask-composite: exclude;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
        z-index: 1;
      }
      &:hover {
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        &::before {
          opacity: 1;
        }
      }
      img {
        width: auto;
        height: auto;
        object-fit: contain;
      }

      /* ENHANCED: Disabled card styles for health check failures */
      &.card-disabled {
        opacity: 0.5;
        cursor: not-allowed !important;
        pointer-events: none;
        filter: grayscale(0.3);

        &::before {
          display: none; /* Remove gradient border on disabled cards */
        }

        &:hover {
          box-shadow: none; /* Remove hover shadow on disabled cards */
          transform: none; /* Remove hover transform on disabled cards */
        }

        /* Disabled state for text elements */
        h2, p {
          color: var(--disabled-text-color, #999) !important;
        }

        /* Disabled state for images */
        img {
          opacity: 0.6;
          filter: grayscale(0.5);
        }
      }

      /* ENHANCED: Loading state styles during health check */
      &.card-loading {
        opacity: 0.7;
        cursor: wait;
        pointer-events: none;

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 20px;
          height: 20px;
          margin: -10px 0 0 -10px;
          border: 2px solid transparent;
          border-top: 2px solid var(--primary-color, #8c65f7);
          border-radius: 50%;
          animation: card-loading-spin 1s linear infinite;
          z-index: 10;
        }
      }
    }
  }

  /* ENHANCED: Loading animation keyframes */
  @keyframes card-loading-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

/* ENHANCED: Health check loading message styles */
.health-check-loading-message {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 1.5rem;
  border-radius: 0.5rem;
  max-width: 600px;
  margin: 0 auto;
  border: 1px solid;
  background-color: var(--info-bg, #d1ecf1);
  border-color: var(--info-border, #bee5eb);
  color: var(--info-text, #0c5460);

  .loading-icon {
    margin-right: 0.75rem;
    font-size: 1.25rem;
    color: var(--info-icon, #17a2b8);

    i {
      display: block;
    }

    .loading-spin {
      animation: loading-spin 1s linear infinite;
    }
  }

  .loading-text {
    flex: 1;

    p {
      margin: 0;
      font-size: 0.95rem;
      font-weight: 500;
      line-height: 1.4;
    }
  }

  /* Light theme specific styles */
  &.light-theme {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;

    .loading-icon {
      color: #17a2b8;
    }
  }

  /* Dark theme specific styles */
  &.dark-theme {
    background-color: #1a2a2e;
    border-color: #2d4a52;
    color: #7dd3fc;

    .loading-icon {
      color: #38bdf8;
    }
  }

  /* Responsive design */
  @media (max-width: 768px) {
    padding: 0.75rem 1rem;
    margin: 0 1rem;

    .loading-icon {
      margin-right: 0.5rem;
      font-size: 1.1rem;
    }

    .loading-text p {
      font-size: 0.9rem;
    }
  }
}

/* ENHANCED: Loading spin animation */
@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design for studio cards */
@media (max-width: 1024px) {
  .studio-cards-grid .studio-card {
    height: 260px;
    img {
      max-height: 120px;
    }
  }
}

@media (max-width: 767px) {
  .studio-cards-grid {
    grid-template-columns: 1fr;
    .studio-card {
      height: 240px;
      img {
        max-height: 100px;
      }
    }
  }
}

@media (max-width: 480px) {
  .studio-cards-grid .studio-card {
    height: 220px;
    img {
      max-height: 80px;
    }
  }
}

@media (min-width: 1420px) {
  .studio-cards-grid {
    max-width: 1200px;
  }
}

/* ENHANCED: Health check info message styles */
.health-check-info-message {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 1.5rem;
  border-radius: 0.5rem;
  max-width: 600px;
  margin: 0 auto;
  border: 1px solid;
  background-color: var(--warning-bg, #fff3cd);
  border-color: var(--warning-border, #ffeaa7);
  color: var(--warning-text, #856404);

  .info-icon {
    margin-right: 0.75rem;
    font-size: 1.25rem;
    color: var(--warning-icon, #f39c12);

    i {
      display: block;
    }
  }

  .info-text {
    flex: 1;

    p {
      margin: 0;
      font-size: 0.95rem;
      font-weight: 500;
      line-height: 1.4;
    }
  }

  /* Light theme specific styles */
  &.light-theme {
    background-color: var(--code-viewer-bg, #fff3cd);
    border-color: #ffeaa7;
    color: #856404;

    .info-icon {
      color: #f39c12;
    }
  }

  /* Dark theme specific styles */
  &.dark-theme {
    background-color: var(--code-viewer-bg, #2d2a1f);
    border-color: #4a4332;
    color: #ffd93d;

    .info-icon {
      color: #ffa502;
    }
  }

  /* Responsive design */
  @media (max-width: 768px) {
    padding: 0.75rem 1rem;
    margin: 0 1rem;

    .info-icon {
      margin-right: 0.5rem;
      font-size: 1.1rem;
    }

    .info-text p {
      font-size: 0.9rem;
    }
  }
}

<main class="container-fluid" id="main-content-container">
  <section class="container-fluid d-flex justify-content-center">
    <app-hero-section-header
      [headerTitle]="'Dream Build Launch!'"
      [headerDescription]="'<PERSON><PERSON><PERSON><PERSON> blends Art Science to bring your ideas to life.'"
      [subHeading]="'What would you like to build today?'"></app-hero-section-header>
  </section>
  <section class="container-fluid d-flex justify-content-center">
    <div class="studio-cards-grid mx-auto p-3 gap-4 max-w-1000">
      @for (card of studioCards(); track trackByCardId($index, card)) {
        <article
          class="studio-card rounded-2xl overflow-hidden position-relative shadow-card h-280"
          [class.cursor-pointer]="!card.disabled"
          [class.card-disabled]="card.disabled"
          [class.card-loading]="card.isLoading"
          [class.image-loading]="isImageLoading(card.id)"
          [class.image-loaded]="isImageLoaded(card.id)"
          (click)="card.disabled ? null : navigateToStudio(card, $event)"
          [style.background-color]="cardBackground()"
          [style.border-color]="cardBorderColor()"
          [style.border-width]="'0.5px'"
          [style.border-style]="'solid'"
          [style.pointer-events]="card.disabled ? 'none' : 'auto'"
          [style.cursor]="card.disabled ? 'not-allowed' : 'pointer'"
          [attr.aria-disabled]="card.disabled"
          [attr.data-card-id]="card.id"
          [attr.tabindex]="card.disabled ? -1 : 0"
          role="button"
          [attr.aria-label]="card.title + ' - ' + card.description + (card.disabled ? ' (disabled)' : '')">
          <div class="d-flex justify-content-between p-4 h-100">
            <div class="d-flex flex-column justify-content-between pe-4 flex-fill">
              <h2
                class="font-mulish font-weight-700 line-height-1-2 text-28 mb-3"
                [style.color]="cardTextColor()">
                {{ card.title }}
              </h2>
              <p
                class="font-mulish font-weight-500 line-height-1-5 text-16 mb-4"
                [style.color]="cardDescriptionColor()">
                {{ card.description }}
              </p>
            </div>
            <div class="d-flex align-items-center justify-content-center flex-fill image-container">
              <!-- Skeleton loader for images -->
              @if (isImageLoading(card.id)) {
                <div class="image-skeleton" [attr.aria-label]="'Loading ' + card.title + ' image'">
                  <div class="skeleton-shimmer"></div>
                </div>
              }

              <!-- Optimized SVG image with progressive loading -->
              <img
                [src]="getOptimizedImageSrc(card)"
                [alt]="card.title + ' illustration'"
                [loading]="card.priority === 'high' ? 'eager' : 'lazy'"
                [attr.fetchpriority]="getImagePriority(card)"
                decoding="async"
                [class.fade-in]="isImageLoaded(card.id)"
                [class.hidden]="isImageLoading(card.id)"
                (load)="onImageLoad(card.id)"
                (error)="onImageError(card.id)" />
            </div>
          </div>
        </article>
      }
    </div>
  </section>

  <!-- ENHANCED: Health check info message section -->
  @if (!areServicesHealthy && !isHealthCheckLoading) {
    <section class="mt-4">
      <div class="d-flex align-items-center justify-content-center">
        <div class="health-check-info-message"
             [class.light-theme]="currentTheme() === 'light'"
             [class.dark-theme]="currentTheme() === 'dark'">
          <div class="info-icon">
              <img
              src="assets/icons/awe_info_t.svg"
              alt="Info"
              class="info-theme-svg"
              [ngClass]="currentTheme() === 'dark' ? 'dark' : 'light'"
              style="margin-right: 6px; vertical-align: middle; width: 18px; height: 18px;"
            />
          </div>
          <div class="info-text">
            <p class="mb-0">{{ healthCheckErrorMessage || 'App Services are down. Please Contact admin to restart the service' }}</p>
          </div>
        </div>
      </div>
    </section>
  }

  <!-- ENHANCED: Health check loading message section -->
  @if (isHealthCheckLoading) {
    <section class="mt-4">
      <div class="d-flex align-items-center justify-content-center">
        <div class="health-check-loading-message"
             [class.light-theme]="currentTheme() === 'light'"
             [class.dark-theme]="currentTheme() === 'dark'">
          <div class="loading-icon">
            <i class="bi bi-arrow-clockwise loading-spin"></i>
          </div>
          <div class="loading-text">
            <p class="mb-0">Checking service availability...</p>
          </div>
        </div>
      </div>
    </section>
  }

  <section class="mt-5">
    <div class="d-flex align-items-center justify-content-center">
      <img
        [src]="dividerImage()"
        class="w-80 max-w-800 h-auto"
        tabindex="0"
        alt="divider"
        loading="lazy"
        decoding="async"
        fetchpriority="low" />
    </div>
  </section>

  <section class="mt-4 py-5">
    <app-recent-creation></app-recent-creation>
  </section>
</main>

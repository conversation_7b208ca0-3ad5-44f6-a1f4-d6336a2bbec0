<main class="container-fluid" id="main-content-container">
  <section class="container-fluid d-flex justify-content-center">
    <app-hero-section-header
      [headerTitle]="'Dream Build Launch!'"
      [headerDescription]="'<PERSON><PERSON><PERSON><PERSON> blends Art Science to bring your ideas to life.'"
      [subHeading]="'What would you like to build today?'"></app-hero-section-header>
  </section>
  <section class="container-fluid d-flex justify-content-center">
    <div class="studio-cards-grid mx-auto p-3 gap-4 max-w-1000">
      @for (card of studioCards; track trackByCardId($index, card)) {
        <article
          class="studio-card rounded-2xl overflow-hidden cursor-pointer position-relative shadow-card h-280"
          (click)="navigateToStudio(card, $event)"
          [class.card-disabled]="card.disabled"
          [class.image-loading]="isImageLoading(card.id)"
          [class.image-loaded]="isImageLoaded(card.id)"
          [style.background-color]="cardBackground()"
          [style.border-color]="cardBorderColor()"
          [style.border-width]="'0.5px'"
          [style.border-style]="'solid'"
          [attr.aria-disabled]="card.disabled"
          [attr.data-card-id]="card.id"
          role="button"
          tabindex="0"
          [attr.aria-label]="card.title + ' - ' + card.description">
          <div class="d-flex justify-content-between p-4 h-100">
            <div class="d-flex flex-column justify-content-between pe-4 flex-fill">
              <h2
                class="font-mulish font-weight-700 line-height-1-2 text-28 mb-3"
                [style.color]="cardTextColor()">
                {{ card.title }}
              </h2>
              <p
                class="font-mulish font-weight-500 line-height-1-5 text-16 mb-4"
                [style.color]="cardDescriptionColor()">
                {{ card.description }}
              </p>
            </div>
            <div class="d-flex align-items-center justify-content-center flex-fill image-container">
              <!-- Skeleton loader for images -->
              @if (isImageLoading(card.id)) {
                <div class="image-skeleton" [attr.aria-label]="'Loading ' + card.title + ' image'">
                  <div class="skeleton-shimmer"></div>
                </div>
              }

              <!-- Optimized SVG image with progressive loading -->
              <img
                [src]="getOptimizedImageSrc(card)"
                [alt]="card.title + ' illustration'"
                [loading]="card.priority === 'high' ? 'eager' : 'lazy'"
                [attr.fetchpriority]="getImagePriority(card)"
                decoding="async"
                [class.fade-in]="isImageLoaded(card.id)"
                [class.hidden]="isImageLoading(card.id)"
                (load)="onImageLoad(card.id)"
                (error)="onImageError(card.id)" />
            </div>
          </div>
        </article>
      }
    </div>
  </section>
  <section class="mt-5">
    <div class="d-flex align-items-center justify-content-center">
      <img
        [src]="dividerImage()"
        class="w-80 max-w-800 h-auto"
        tabindex="0"
        alt="divider"
        loading="lazy"
        decoding="async"
        fetchpriority="low" />
    </div>
  </section>

  <section class="mt-4 py-5">
    <app-recent-creation></app-recent-creation>
  </section>
</main>

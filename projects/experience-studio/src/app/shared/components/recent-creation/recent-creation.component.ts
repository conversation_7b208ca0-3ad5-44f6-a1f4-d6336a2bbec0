import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy, CUSTOM_ELEMENTS_SCHEMA, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { HeadingComponent, BodyTextComponent, CardsComponent } from '@awe/play-comp-library';
import {
  RecentProjectService,
  Project,
} from '../../services/recent-project-services/recent-project.service';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { CardOption } from '../../models/recent-creation.model';
import { SubscriptionManager, ObserverManager } from '../../utils/subscription-management.util';

@Component({
  selector: 'app-recent-creation',
  imports: [CommonModule, HeadingComponent, BodyTextComponent, CardsComponent],
  standalone: true,
  templateUrl: './recent-creation.component.html',
  styleUrl: './recent-creation.component.scss',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class RecentCreationComponent implements OnInit, OnDestroy {
  theme: 'light' | 'dark' = 'light';
  selectedId: string | null = null;
  currentCategory: string = 'recent';
  isLoading: boolean = true;
  hasError: boolean = false;
  options: { [category: string]: CardOption[] } = { recent: [], all: [] };

  private subscriptionManager = new SubscriptionManager();
  private observerManager = new ObserverManager();

  // Flag to prevent duplicate API calls from multiple triggers
  private projectsLoaded = false;
  private isLoadingProjects = false;

  constructor(
    private recentProjectService: RecentProjectService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.initializePlaceholders();
    this.setupIntersectionObserver();

    // Load projects immediately if we're already on the landing page
    if (this.router.url.includes('/experience/main')) {
      this.loadProjectsIfNeeded();
    }

    // Set up subscription to load projects when navigating to the landing page
    // Only load if not already loaded to prevent duplicate API calls
    this.subscriptionManager.subscribe(
      this.router.events.pipe(filter(event => event instanceof NavigationEnd)),
      () => {
        if (this.router.url.includes('/experience/main')) {
          this.loadProjectsIfNeeded();
        }
      }
    );
  }

  ngOnDestroy(): void {
    // Clean up observers
    this.observerManager.cleanup();

    // Reset flags for potential future use
    this.projectsLoaded = false;
    this.isLoadingProjects = false;
  }

  switchCategory(category: string): void {
    if (this.currentCategory !== category) {
      this.currentCategory = category;
      this.animateCategorySwitch(category);
      this.cdr.markForCheck(); // Trigger change detection
    }
  }

  getCurrentOptions(): CardOption[] {
    return this.options[this.currentCategory] || [];
  }

  handleSelection(id: string, event?: Event): void {
    if (event) event.preventDefault();
    this.selectedId = id;
    this.cdr.markForCheck(); // Trigger change detection
  }

  getDefaultActionText(type: string): string {
    const actionMap: { [key: string]: string } = {
      ui: 'Generate UI',
      app: 'Generate App',
      analysis: 'Design Analysis',
      accessibility: 'Review Accessibility',
    };
    return actionMap[type.toLowerCase()] || 'View';
  }


  trackByFn(index: number, item: CardOption): string {
    if (!item.id || item.id.trim() === '') {
      return `empty-${index}-${this.currentCategory}`;
    }
    if (item.id.startsWith('placeholder-')) {
      return `${item.id}-${this.currentCategory}`;
    }
    return item.id;
  }

  private setupIntersectionObserver(): void {
    const element = document.querySelector('.recent-creation-wrapper');
    if (element) {
      const observer = this.observerManager.createIntersectionObserver(
        entries => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              this.loadProjectsIfNeeded();
              observer.disconnect();
            }
          });
        },
        {
          root: null,
          rootMargin: '100px',
          threshold: 0.1,
        }
      );
      observer.observe(element);
    } else {
      this.loadProjectsIfNeeded();
    }
  }

  private initializePlaceholders(): void {
    this.options['recent'] = Array(4)
      .fill(null)
      .map((_, index) => this.createPlaceholder(`recent-${index}`));
    this.options['all'] = Array(12)
      .fill(null)
      .map((_, index) => this.createPlaceholder(`all-${index}`));
  }

  private createPlaceholder(uniqueId: string): CardOption {
    return {
      id: `placeholder-${uniqueId}`,
      heading: '',
      description: '',
      type: 'placeholder',
      timestamp: ''
    };
  }

  /**
   * Load projects only if they haven't been loaded yet and not currently loading
   * This prevents duplicate API calls from multiple triggers
   */
  private loadProjectsIfNeeded(): void {
    if (this.projectsLoaded) {
    }

    if (this.isLoadingProjects) {
      return;
    }

    this.loadProjects();
  }

  private loadProjects(): void {
    this.isLoading = true;
    this.isLoadingProjects = true;
    this.hasError = false;

    // Make only ONE API call to get all projects (12 items)
    // Then derive both 'recent' (4 items) and 'all' (12 items) from the same response
    this.subscriptionManager.subscribe(
      this.recentProjectService.getUserProjects('<EMAIL>', 12),
      response => this.handleProjectsLoad(response),
      error => {
        this.isLoading = false;
        this.isLoadingProjects = false;
        this.hasError = true;
        this.cdr.markForCheck();
      }
    );
  }

  private handleProjectsLoad(response: any): void {
    requestAnimationFrame(() => {
      const allProjects = this.mapProjectsToCardOptions(response.projects);

      // Derive both categories from the same API response
      this.options['all'] = allProjects; // All 12 projects
      this.options['recent'] = allProjects.slice(0, 4); // First 4 projects for recent

      // Update flags to indicate projects are loaded
      this.isLoading = false;
      this.isLoadingProjects = false;
      this.projectsLoaded = true;

      this.cdr.markForCheck(); // Trigger change detection after updating data
    });
  }

  private truncateDescription(description: string): string {
    const words = description.split(' ');
    return words.length > 10 ? words.slice(0, 10).join(' ') + '...' : description;
  }

  private mapProjectsToCardOptions(projects: Project[]): CardOption[] {
    // ENHANCED: Filter out projects with null project_type before mapping
    const validProjects = projects.filter(project => {
      const isValid = project.project_type !== null && project.project_type !== undefined;
      if (!isValid) {
      }
      return isValid;
    });

    return validProjects.map(project => ({
      id: project.project_id,
      heading: project.project_name,
      description: this.truncateDescription(project.project_description.replace(/^"|"$/g, '')),
      type: project.project_type!.toLowerCase(), // Safe to use ! since we filtered out nulls
      timestamp: this.recentProjectService.formatDate(project.last_modified),
    }));
  }

  private animateCategorySwitch(category: string): void {
    const gridElement = document.querySelector('.cards-grid') as HTMLElement;
    if (gridElement) {
      gridElement.classList.remove('slide-recent', 'slide-all');
      void gridElement.offsetWidth;
      gridElement.classList.add(category === 'recent' ? 'slide-recent' : 'slide-all');
    }
  }
}

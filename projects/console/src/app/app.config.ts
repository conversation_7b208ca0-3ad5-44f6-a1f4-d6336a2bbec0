 
import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';
import { MarkdownModule } from 'ngx-markdown';
import { importProvidersFrom } from '@angular/core';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptors, withInterceptorsFromDi } from '@angular/common/http';
import { routes } from './app.routes';
import { LoaderInterceptor } from './shared/interceptors/loader.interceptor';
import { AuthInterceptor } from '@shared/auth/interceptors/auth.interceptor';
import {
  LucideAngularModule, User, Settings, Info, ChevronLeft, ChevronRight, XCircle, CircleCheck,
  AlignVerticalDistributeStart, CircleCheckBig, Play, MoveLeft, CalendarDays, EllipsisVertical, SquarePen, Wifi, Search, AlertCircle, EyeOff, Mail, Phone, Check, X, Lock, Edit, Trash, Plus, Minus, Eye, Home, Layout, ChevronDown, ChevronUp, Bell, Grid, Star, Leaf, CheckCircle, AlertTriangle, XOctagon, Sparkles, Slash, Feather, Globe, Send, Box, Paperclip
 
 
} from 'lucide-angular';
export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }), 
    provideRouter(routes),
    provideHttpClient(
      withInterceptors([LoaderInterceptor])
    ),
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    provideHttpClient(withInterceptorsFromDi()),
    importProvidersFrom(LucideAngularModule.pick({
      User, Settings, Info, ChevronLeft, ChevronRight,
      XCircle, CircleCheck, AlignVerticalDistributeStart, CircleCheckBig, MoveLeft, Play, CalendarDays, EllipsisVertical, SquarePen, Wifi, Search, AlertCircle, EyeOff, Mail, Phone, Check, X, Lock, Edit, Trash, Plus, Minus, ChevronDown, ChevronUp, Eye, Home, Layout, Bell, Grid, Star, Leaf, CheckCircle, AlertTriangle, XOctagon, Sparkles, Slash, Feather, Globe, Send, Box, Paperclip
    }), MarkdownModule.forRoot())
  ]
};  
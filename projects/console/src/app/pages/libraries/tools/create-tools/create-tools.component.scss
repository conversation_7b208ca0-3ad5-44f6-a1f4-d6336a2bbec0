.create-tools-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: transparent;
}

.page-title{
  font-weight: 600;
  font-size: 20px;
}

// Chat container styles
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 400px;
  border: 1px solid #e1e4e8; // Single border to match layout
  border-radius: 4px; // Small radius to match cards
  overflow: hidden;
  position: relative; // Needed for absolute positioning of child elements
  background: #ffffff;
}

.playground-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--dashboard-primary);
  margin: 0 0 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Make sure chat interface takes full height
app-chat-interface {
height: 100%;
display: flex;
flex-direction: column;
flex: 1;
}

form {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 300px); // Fixed height to fit viewport initially
  overflow: hidden;
}

.form-layout {
  display: flex;
  flex-direction: row;
  gap: 0; // No gap - blocks attached to each other
  padding: 0; // No padding - full width
  flex: 1;
  overflow: hidden; // Remove scrollbar from main container
  height: 100%; // Take full available height
  border: 1px solid #e1e4e8; // Single border around entire layout
  background: #ffffff;

  &.three-column-layout {
    .left-column {
      width: 25%;
      transition: width 0.3s ease;
      @media (max-width: 1400px) {
        width: 25%;
      }
      @media (max-width: 1200px) {
        width: 100%;
      }
    }
    .middle-column {
      width: 40%;
      transition: width 0.3s ease;
      @media (max-width: 1400px) {
        width: 35%;
      }
      @media (max-width: 1200px) {
        width: 100%;
      }
    }
    .rightEnd-column {
    width: 35%;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    
    @media (max-width: 1400px) {
      width: 60%;
    }
    
    @media (max-width: 1200px) {
      width: 60%;
    }
  }
  }
  @media (max-width: 1400px) {
    gap: 16px;
    padding: 16px;
  }
  @media (max-width: 1200px) {
    flex-wrap: wrap;
  }
  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px;
    flex-direction: column;
  }
.middle-column{
  padding: 0px !important;
}
  .left-column, .middle-column, .chat-column {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%; // Use full height of parent container
    padding: 16px; // Inner padding for column content
    background: #ffffff; // White background for columns
    border-right: 1px solid #e1e4e8; // Only right border to separate columns
    overflow-y: auto; // Add scroll to individual columns if needed

    &:last-child {
      border-right: none; // Remove border from last column
    }
    @media (max-width: 1400px) {
      gap: 16px;
      padding: 16px;
    }
    @media (max-width: 1200px) {
      height: auto;
      padding: 16px;
      border-right: none; // Remove borders on mobile
      border-bottom: 1px solid #e1e4e8; // Add bottom border on mobile
      overflow-y: visible; // Remove scroll on mobile

      &:last-child {
        border-bottom: none;
      }
    }
    @media (max-width: 576px) {
      gap: 12px;
      width: 100% !important;
      padding: 12px;
    }
  }

  .left-column {
      width: 40%;
      flex-shrink: 0;
      transition: width 0.3s ease;
      @media (max-width: 1400px) {
        width: 40%;
      }
      @media (max-width: 1200px) {
        width: 100%;
      }
    app-card {
      flex-shrink: 0;
    }
    // Make first card (Tool Details) fixed height
    app-card:first-of-type {
      flex: 0 0 auto;
    }
    // Make second card (Assign Filters) scrollable
    app-card:last-of-type {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      .card-content {
        overflow-y: auto;
      }
    }
  }

  .middle-column {
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      min-width: 300px; // Ensure minimum width

      // Default width when no chat interface
      width: 60%;

      @media (max-width: 1200px) {
        width: 100%;
        min-width: unset; // Remove min-width on mobile
      }

    .middle-column-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    app-card {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }

  .rightEnd-column {
    width: 35%;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    
    @media (max-width: 1400px) {
      width: 60%;
    }
    
    @media (max-width: 1200px) {
      width: 60%;
    }
  }
  .chat-column {
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        background-color: var(--agent-chat-column-bg);
        border-left: 1px solid var(--agent-chat-column-border);
        box-shadow: -2px 0 10px var(--agent-chat-column-shadow);
        min-width: 250px; // Ensure minimum width
        height: 100%; // Ensure full height

        // Default width
        width: 35%;

        // Make sure app-card takes full height
        app-card {
          flex: 1;
          display: flex;
          flex-direction: column;
          height: 100%;
        }

        @media (max-width: 1200px) {
          width: 100%;
          min-width: unset; // Remove min-width on mobile
          border-left: none;
          border-top: 1px solid var(--agent-chat-column-border);
          box-shadow: 0 -2px 10px var(--agent-chat-column-shadow);
        }

    .chat-column-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    app-card {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}

.card-content {
  display: flex;
  flex-direction: column;
}

// Middle and chat columns need specific height
// Middle and chat columns need specific height
.middle-column .card-content,
.chat-column .card-content {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Make sure chat card content takes full height
.chat-column .card-content {
  padding-bottom: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1; // Add flex: 1 to take full available height

  .chat-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;
    overflow: hidden; // Let chat interface handle its own scrolling
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px;
  color: var(--text-color);

  @media (max-width: 576px) {
    font-size: 14px;
    margin-bottom: 6px;
  }
}

// Override code-editor-container border in create-tools component
::ng-deep .code-editor-container {
  border: none !important;
}

// Add border between code editor and validation editor
.validation-output-section {
  border-top: 1px solid #e1e4e8;
  margin-top: 16px;
  padding-top: 16px;
}

.middle-column-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 33px 13px 4px;
  margin-top: auto;
  margin-bottom: 0;
  flex-shrink: 0;

  @media (max-width: 576px) {
    gap: 12px;
    padding: 12px 0 0;
    margin-top: auto;
    margin-bottom: 0;
  }

  .exit-button, .save-button, .execute-button {
    padding: 10px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    @media (max-width: 576px) {
      padding: 8px 16px;
      font-size: 13px;
    }
  }

  .exit-button {
    background-color: transparent;
    border: 1px solid var(--button-secondary-border);
    color: var(--button-secondary-text);

    &:hover {
      background-color: var(--button-secondary-hover-bg);
    }
  }

  .save-button, .execute-button {
    background: var(--button-gradient);
    border: none;
    color: var(--button-primary-text);

    &:hover {
      opacity: var(--button-hover-opacity);
    }
  }

  .execute-button {
      background: var(--button-gradient);
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px var(--dashboard-shadow-hover);
      }
    }
}

.note {
    font-weight: 400;
    color: #A3A7C2;
    font-size: 12px;
}

::ng-deep .card-container{
  padding: 0px !important;
  box-shadow: none !important;
  height: 100% !important; // Ensure cards take full height
  display: flex !important;
  flex-direction: column !important;

  // Override theme's hover shadow as well
  &.clickable:hover:not(.no-hover) {
    box-shadow: none !important; // Remove hover shadow too
  }
}

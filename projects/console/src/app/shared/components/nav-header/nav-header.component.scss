::ng-deep .outer-box.light {
  background-color: transparent !important;
  box-shadow: none !important;
}

::ng-deep .container {
  background-color: transparent !important;
}

// Navigation menu styles
.nav-menu {
  display: flex;
  align-items: center;
  height: 100%;
  position: relative;
  z-index: 950;
}

.nav-items {
  display: flex;
  align-items: center;
  // gap: 12px;
  height: 100%;
}

.nav-item-wrapper {
  position: relative;
  z-index: 950;
}

.nav-item-container {
  position: relative;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  color: var(--nav-text, #666D99);
  cursor: pointer;
  transition: all 0.3s ease;

  // &:hover {
  //   background-color: var(--nav-hover, rgba(255, 255, 255, 0.1));
  // }

  &.selected {
    background-color: var(--nav-active, #fff);
    color: var(--nav-active-text, #000);
    box-shadow: 0 2px 4px var(--card-shadow, rgba(0, 0, 0, 0.1));
  }

  .item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: currentColor;
  }

  .dropdown-arrow {
    margin-left: 4px;
    transition: transform 0.2s ease;

    &.open {
      transform: rotate(180deg);
    }
  }
}

// Dropdown menu styles
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 280px;
  background-color: var(--dropdown-bg);
  border-radius: 8px;
  box-shadow: 0 4px 16px var(--dropdown-shadow);
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 1000;
  margin-top: 8px;
  padding: 8px;

  &.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: var(--dropdown-hover-bg);
  }

  .dropdown-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--nav-item-color);
    flex-shrink: 0;
  }

  .dropdown-item-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .dropdown-item-title {
    font-weight: 500;
    font-size: 16px;
    color: var(--dropdown-text);
  }

  .dropdown-item-desc {
    font-size: 14px;
    color: var(--text-secondary);
  }
}

// Theme toggle icon and user profile styling
.header-right-content {
  img {
    width: 24px;
    height: 24px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }
}

// Profile dropdown container
.profile-container {
  position: relative;
  display: flex;
  align-items: center;

  // Profile icon wrapper with nav-item-like styling
  .cursor-pointer {
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
      background-color: var(--nav-item-active-bg, #e6e4fb);

      &:after {
        opacity: 0.1;
      }
    }

    // When profile dropdown is open, apply selected state styling
    &.active {
      background-color: var(--nav-item-active-bg, #e6e4fb);
      box-shadow: 0 2px 4px var(--card-shadow, rgba(0, 0, 0, 0.1));

      &:after {
        opacity: 0;
      }
    }

    // Subtle gradient overlay similar to nav items
    &:after {
      content: '';
      position: absolute;
      inset: 0;
      background: var(--gradient-primary);
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: -1;
    }
  }
}

// Profile dropdown styles (matching navbar dropdown)
.profile-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 200px;
  background-color: var(--dropdown-bg);
  border-radius: 8px;
  box-shadow: 0 4px 16px var(--dropdown-shadow);
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 1000;
  margin-top: 8px;
  padding: 8px;

  &.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.profile-dropdown-content {
  display: flex;
  flex-direction: column;
}

.profile-info {
  padding: 12px;
  border-radius: 6px;
}

.profile-name {
  font-weight: 500;
  font-size: 16px;
  color: var(--dropdown-text);
  margin-bottom: 4px;
}

.profile-email {
  font-size: 14px;
  color: var(--text-secondary);
}

.profile-divider {
  height: 1px;
  background-color: var(--border-color, #E5E7EB);
  margin: 8px 0;
}

.profile-actions {
  display: flex;
  flex-direction: column;
}

.profile-action-item {
  color: var(--nav-text, #666D99);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    background-color: var(--nav-hover);
    color: var(--nav-item-active-color);
    
    &:after {
      opacity: 0.1;
    }
  }
  
  // Add the gradient overlay effect
  &:after {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  .action-label {
    font-weight: 500;
    font-size: 16px;
    color: var(--dropdown-text);
  }
}

@media (max-width: 400px) {
  ::ng-deep .outer-box .center-content-wrapper {
    display: none !important;
  }
}

.org-path-dropdowns {
  display: flex;
  align-items: center;
  gap: 8px;
  .app-select-dropdown {
    min-width: 140px;
    max-width: 180px;
  }
}

.org-path-trigger {
  display: flex;
  align-items: center;
  background: var(--nav-bg);
  border-radius: 24px;
  padding: 0 8px 0 8px;
  height: 48px;
  min-width: 156px;
  box-shadow: 0 0 0 1px var(--nav-border);
  cursor: pointer;
  transition: box-shadow 0.2s;

  .org-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: transparent;
    margin-right: 8px;
    flex-shrink: 0;
    svg {
      display: block;
    }
  }

  .org-label-text {
    font-weight: 600;
    color: var(--nav-text, #111);
    display: flex;
    align-items: center;
    margin-right: 8px;
    white-space: nowrap;
  }

  .org-dropdown-arrow {
    display: flex;
    align-items: center;
    margin-left: auto;
    color: var(--nav-arrow, #222);
    transition: transform 0.2s;
    svg {
      display: block;
      transition: transform 0.4s ease-in-out;
    }
    &.open svg {
      transform: rotate(180deg);
    }
  }
}

.org-path-dropdown-container {
  position: relative;
  display: inline-block;
}

.org-path-popover {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 8px;
  z-index: 3001;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  border: 1px solid #e0e0e0;
  padding: 20px 24px 16px 24px;
  width: 320px;
  min-width: 250px;
  max-width: calc(100vw - 32px);
  max-height: calc(100vh - 100px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  transition: box-shadow 0.2s;
}

.org-path-popover.right {
  left: auto;
  right: 0;
}

.org-path-popover.left {
  left: 0;
  right: auto;
}

.org-path-backdrop {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: transparent;
  z-index: 3000;
}

.dropdown-row-horizontal {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
  margin-bottom: 16px;
}

.dropdown-row-vertical {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: stretch;
  margin-bottom: 16px;
}

.popover-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  width: 100%;
  margin-top: 8px;
}

.org-icon img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: block;
  object-fit: cover;
}

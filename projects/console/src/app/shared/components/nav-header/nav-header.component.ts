import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Host<PERSON><PERSON><PERSON>, ElementRef, ChangeDetectorRef, ViewChild, Renderer2, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from '@awe/play-comp-library';
import { NavItemComponent } from '../nav-item/nav-item.component';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { ThemeService } from '../../services/theme/theme.service';
import { ThemeToggleComponent } from '../theme-toggle/theme-toggle.component';
import { environment } from '../../../../environments/environment';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { AuthService } from '@shared/auth/services/auth.service';
import { SelectDropdownComponent, SelectOption } from '../../components/select-dropdown/select-dropdown.component';
import { OrgConfigService } from '../../../pages/org-config/services/org-config.service';
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { ButtonComponent } from '../button/button.component';

interface DropdownItem {
  label: string;
  description: string;
  route: string;
  icon: string;
}

interface NavItem {
  label: string;
  route: string;
  selected: boolean;
  hasDropdown: boolean;
  dropdownOpen?: boolean;
  icon: string;
  dropdownItems?: DropdownItem[];
}

@Component({
  selector: 'app-nav-header',
  standalone: true,
  imports: [HeaderComponent, CommonModule, NavItemComponent, ThemeToggleComponent, SelectDropdownComponent, ButtonComponent],
  providers: [OrgConfigService],
  templateUrl: './nav-header.component.html',
  styleUrl: './nav-header.component.scss',
})
export class NavHeaderComponent implements OnInit, OnDestroy, AfterViewInit {
  logoSrc: string = '';
  themeMenuIcon: string = '';
  userAvatar: string = '';
  currentTheme: 'light' | 'dark' = 'light';

  // Profile dropdown properties
  profileDropdownOpen: boolean = false;
  userName: string = '';
  userEmail: string = '';
  public redirectUrl = '';

  // Subscription management
  private subscriptions = new Subscription();

  // Navigation menu items
  navItems: NavItem[] = [
    { 
      label: 'Dashboard', 
      route: '/dashboard', 
      selected: true,
      hasDropdown: false,
      icon: `svgs/icons/awe_dashboard.svg`
    },
    { 
      label: 'Launch', 
      route: '/launch', 
      selected: false, 
      hasDropdown: true,
      dropdownOpen: false,
      icon: `svgs/icons/awe_launch.svg`,
      dropdownItems: [
        { 
          label: 'Agents', 
          description: 'Create, Manage and Edit Agents',
          route: '/launch/agents',
          icon: `svgs/icons/awe_agents.svg`
        },
        { 
          label: 'Workflows', 
          description: 'Create, Manage and Edit Workflows',
          route: '/launch/workflows',
          icon: `svgs/icons/awe_workflows.svg`
        }
      ]
    },
    { 
      label: 'Libraries', 
      route: '/libraries', 
      selected: false, 
      hasDropdown: true,
      dropdownOpen: false,
      icon: `svgs/icons/awe_libraries.svg`,
      dropdownItems: [
        { 
          label: 'Prompts', 
          description: 'Create, Manage and Edit Prompts', 
          route: '/libraries/prompts',
          icon: `svgs/icons/awe_prompts.svg`
        },
        { 
          label: 'Models', 
          description: 'Add, Manage and Edit Models', 
          route: '/libraries/models',
          icon: `svgs/icons/awe_models.svg`
        },
        { 
          label: 'Knowledge-base', 
          description: 'Add, Manage and Edit Knowledge-Base', 
          route: '/libraries/knowledge-base',
          icon: `svgs/icons/awe_knowledgebase.svg`
        },
        { 
          label: 'Tools', 
          description: 'Add, Manage and Edit Tools', 
          route: '/libraries/tools',
          icon: `svgs/icons/awe_tools.svg`
        },
        { 
          label: 'Guardrails', 
          description: 'Add, Manage and Edit Guardrails', 
          route: '/libraries/guardrails',
          icon: `svgs/icons/awe_guardrails.svg`
        }
      ]
    },
    { 
      label: 'Manage', 
      route: '/manage', 
      selected: false, 
      hasDropdown: true,
      dropdownOpen: false,
      icon: `svgs/icons/awe_manage.svg`,
      dropdownItems: [
        { 
          label: 'Management 1', 
          description: 'Management description', 
          route: '/manage1',
          icon: `svgs/icons/awe_admin_ management.svg`
        },
        { 
          label: 'Users & Admins Management', 
          description: 'Add, Manage Users and Admins, Modify tokens and assign filters', 
          route: '/manage/admin-management',
          icon: `svgs/icons/awe_admin_ management.svg`
        }
      ]
    },
    { 
      label: 'Analytics', 
      route: '/analytics', 
      selected: false, 
      hasDropdown: true,
      dropdownOpen: false,
      icon: `svgs/icons/awe_analytics.svg`,
      dropdownItems: [
        { 
          label: 'Analytics 1', 
          description: 'Analytics description', 
          route: '/analytics1',
          icon: `svgs/icons/awe_analytics.svg`
        },
        { 
          label: 'Analytics 2', 
          description: 'Analytics description', 
          route: '/analytics2',
          icon: `svgs/icons/awe_analytics.svg`
        }
      ]
    }
  ];

  // Org path dropdown state
  orgDropdownOptions: SelectOption[] = [];
  domainDropdownOptions: SelectOption[] = [];
  projectDropdownOptions: SelectOption[] = [];
  teamDropdownOptions: SelectOption[] = [];
  selectedOrg: string = '';
  selectedDomain: string = '';
  selectedProject: string = '';
  selectedTeam: string = '';

  // Track if dropdowns are open
  isOrgOpen = false;
  isDomainOpen = false;
  isProjectOpen = false;
  isTeamOpen = false;

  isOrgDialogOpen = false;

  headerConfigForm!: FormGroup;
  dropdownValues: { [key: string]: any[] } = {
    org: [], domain: [], project: [], team: []
  };
  selectedDropdownValues: { [key: string]: string } = {
    org: '', domain: '', project: '', team: ''
  };
  pathId: number[] = [];

  @ViewChild('orgPathTrigger', { static: false }) orgPathTrigger!: ElementRef;
  @ViewChild('popover', { static: false }) popoverRef!: ElementRef;

  popoverAlign: 'left' | 'right' = 'left';

  constructor(
    private elementRef: ElementRef,
    private router: Router,
    private themeService: ThemeService,
    private tokenStorage: TokenStorageService,
    private authService: AuthService,
    private orgConfigService: OrgConfigService,
    private formBuilder: FormBuilder,
    private cdr: ChangeDetectorRef,
    private renderer: Renderer2
  ) {
    // Initialize headerConfigForm before any logic that uses it
    this.headerConfigForm = this.formBuilder.group({
      org: ['', Validators.required],
      domain: ['', Validators.required],
      project: ['', Validators.required],
      team: ['', Validators.required],
    });
    // Subscribe to router events to update the active menu item
    this.subscriptions.add(
      this.router.events
        .pipe(filter(event => event instanceof NavigationEnd))
        .subscribe((event: any) => {
          this.updateActiveMenuItemByRoute(event.url);
        })
    );
  }

  ngOnInit(): void {
    // Get redirect URL from auth service configuration
    const authConfig = this.authService.getAuthConfig();
    this.redirectUrl = authConfig?.redirectUrl || window.location.origin;

    // Get the initial theme
    this.currentTheme = this.themeService.getCurrentTheme();

    // Update assets based on theme
    this.updateThemeAssets();

    // Subscribe to theme changes
    this.subscriptions.add(
      this.themeService.themeObservable.subscribe(theme => {
        this.currentTheme = theme;
        this.updateThemeAssets();
      })
    );

    // Load user data from cookies initially
    this.loadUserData();

    // Subscribe to authentication state changes to reload user data when auth completes
    this.subscriptions.add(
      this.authService.authState$.subscribe(isAuthenticated => {
        if (isAuthenticated) {
          // Reload user data when authentication is successful
          this.loadUserData();
        }
      })
    );

    // Initialize the active menu item based on the current route
    this.updateActiveMenuItemByRoute(this.router.url);
  }

  ngAfterViewInit(): void {
  }

  ngOnDestroy(): void {
    // Clean up all subscriptions to prevent memory leaks
    this.subscriptions.unsubscribe();
  }

  // Update assets based on the current theme
  private updateThemeAssets(): void {
    // Update theme-specific assets
    this.logoSrc = `svgs/ascendion-logo/ascendion-logo-${this.currentTheme}.svg`;   
    this.themeMenuIcon = `svgs/header/menu-${this.currentTheme}.svg`;
    this.userAvatar = `svgs/header/user-avatar.svg`;
  }

  // Update the active menu item based on the current route
  updateActiveMenuItemByRoute(url: string): void {
    // Reset all selections
    this.navItems.forEach(item => {
      item.selected = false;
    });

    // Find the matching parent route or parent of a child route
    const parentItem = this.navItems.find(item => {
      // Check if this is a direct match for the parent route
      if (url === item.route) {
        return true;
      }
      
      // Check if this is a dropdown parent with a matching child
      if (item.hasDropdown && item.dropdownItems) {
        // Check if the URL starts with the parent route path (for nested routes)
        // OR if any child route exactly matches the URL
        return url.startsWith(item.route + '/') || 
               item.dropdownItems.some(child => url === child.route);
      }
      
      return false;
    });

    if (parentItem) {
      parentItem.selected = true;
      console.log(`Selected nav item: ${parentItem.label}`);
    } else {
      // Default to Dashboard if no match found
      const dashboardItem = this.navItems.find(item => item.route === '/dashboard');
      if (dashboardItem) {
        dashboardItem.selected = true;
        console.log('No matching route found, defaulting to Dashboard');
      }
    }
  }

  // Listen for clicks on the document to close dropdowns when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    // Check if click target is inside a dropdown menu or profile dropdown
    const target = event.target as HTMLElement;
    const clickedInsideDropdown = !!target.closest('.dropdown-menu') ||
                                  !!target.closest('.dropdown-item') ||
                                  !!target.closest('.profile-dropdown');

    // If clicked inside dropdown, don't close menus
    if (clickedInsideDropdown) {
      console.log('Clicked inside dropdown, keeping open');
      return;
    }

    // Check if the click was outside the nav header
    const clickedOutsideNavHeader = !this.elementRef.nativeElement.contains(event.target);
    if (clickedOutsideNavHeader) {
      console.log('Clicked outside header, closing all dropdowns');
      this.closeAllDropdowns();
      this.closeProfileDropdown();
    }
  }

  // Toggle dropdown menu
  toggleDropdown(index: number): void {
    console.log(`Toggling dropdown for index: ${index}`);

    // Close profile dropdown when nav dropdown opens
    this.closeProfileDropdown();

    // Close all other dropdowns
    this.navItems.forEach((navItem, i) => {
      if (i !== index && navItem.dropdownOpen) {
        navItem.dropdownOpen = false;
      }
    });

    // Toggle the current dropdown
    this.navItems[index].dropdownOpen = !this.navItems[index].dropdownOpen;
  }

  // Close all dropdowns
  closeAllDropdowns(): void {
    this.navItems.forEach(navItem => {
      if (navItem.dropdownOpen) {
        navItem.dropdownOpen = false;
      }
    });
  }

  // Select a menu item
  selectMenuItem(index: number): void {
    this.navItems.forEach((navItem, i) => {
      navItem.selected = i === index;
    });
  }

  // Navigate to route
  navigateTo(route: string): void {
    console.log(`Navigating to ${route}`);
    this.router.navigate([route]);
  }

  // Handle dropdown item selection
  onDropdownItemSelected(event: {route: string, label: string}, parentIndex: number): void {
    console.log(`Dropdown item selected: ${event.label}, navigating to ${event.route}`);
    this.selectMenuItem(parentIndex);
    this.navigateTo(event.route);

    // Close all dropdowns after selection
    this.closeAllDropdowns();
  }

  // Load user data from cookies
  loadUserData(): void {
    this.userName = this.tokenStorage.getDaName() || 'User';
    this.userEmail = this.tokenStorage.getDaUsername() || '';
  }

  // Toggle profile dropdown
  toggleProfileDropdown(): void {
    this.profileDropdownOpen = !this.profileDropdownOpen;
    // Close nav dropdowns when profile dropdown opens
    if (this.profileDropdownOpen) {
      this.closeAllDropdowns();
    }
  }

  // Close profile dropdown
  closeProfileDropdown(): void {
    this.profileDropdownOpen = false;
  }

  // Handle logout
  logout() {
    if (this.tokenStorage.getLoginType() === 'basic') {
      this.authService.basicLogout().subscribe({
        next: () => {
          // Use Angular router instead of window.location
          this.router.navigate(['/login']);
          this.tokenStorage.deleteCookie('org_path');
        },
        error: (error) => {
          console.error('Basic logout failed:', error);
          // Still try to navigate to login even if logout fails
          this.router.navigate(['/login']);
        }
      });
    } else {
      this.authService.logout(this.redirectUrl).subscribe({
        next: () => {
          this.tokenStorage.deleteCookie('org_path');
        },
        error: (error) => {
          console.error('SSO logout failed:', error);
        }
      });
    }
  }

  initOrgPathFromCookie() {
    const path = this.tokenStorage.getCookie('org_path');
    if (path) {
      const parts = path.split('::');
      const usecasePath = parts[0] || '';
      const usecaseIdPath = parts[1] || '';
      
      // Parse the IDs
      const ids = usecaseIdPath.split('@').map(Number);
      this.pathId = ids;
      
      // Set form values (IDs)
      this.headerConfigForm.patchValue({
        org: ids[0],
        domain: ids[1],
        project: ids[2],
        team: ids[3]
      });
      
      // Parse and set the usecase path values (labels)
      const pathParts = usecasePath.split('@');
      this.selectedDropdownValues = {
        org: pathParts[0] || '',
        domain: pathParts[1] || '',
        project: pathParts[2] || '',
        team: pathParts[3] || ''
      };
      
      // Load dropdown options
      this.getDropdownList('org', 0, -1);
      this.getDropdownList('domain', 1, ids[0]);
      this.getDropdownList('project', 2, ids[1]);
      this.getDropdownList('team', 3, ids[2]);
    } else {
      this.getDropdownList('org', 0, -1);
    }
  }

  getDropdownList(dropdown: string, level: number, parentId: number) {
    this.orgConfigService.getLevelList(level, parentId).subscribe({
      next: (res: any) => {
        if (res && res.levels) {
          this.dropdownValues[dropdown] = res.levels.map((level: any) => ({
            label: level.name,
            value: level.levelId
          }));
        }
      },
      error: error => console.error(error),
    });
  }

  onDropdownSelect(id: any, level: number, group: string, dropdown: string) {
    const levelId = typeof id === 'object' && id !== null && 'value' in id ? id.value : id;
    const selectedOption = this.dropdownValues[group].find((option: any) => option?.value === levelId);
    this.selectedDropdownValues[group] = selectedOption?.label ?? '';
    if (dropdown !== 'not-found') {
      this.getDropdownList(dropdown, level, levelId);
      this.updateSubDropdown(dropdown);
    }
  }

  updateSubDropdown(dropdown: string) {
    const dropdownList: string[] = ['org', 'domain', 'project', 'team'];
    let startIndex = dropdownList.indexOf(dropdown);
    dropdownList.forEach((key, index) => {
      if (index >= startIndex) {
        this.headerConfigForm.get(key)?.setValue(null);
        if (index > startIndex) {
          this.dropdownValues[key] = [];
        }
      }
    });
  }

  toggleOrgDialog() {
    this.isOrgDialogOpen = !this.isOrgDialogOpen;
    if (this.isOrgDialogOpen) {
      this.initOrgPathFromCookie();
      setTimeout(() => this.adjustPopoverAlignment(), 0);
    }
  }

  adjustPopoverAlignment() {
    const trigger = this.orgPathTrigger?.nativeElement;
    const popover = this.popoverRef?.nativeElement;
    if (trigger && popover) {
      const triggerRect = trigger.getBoundingClientRect();
      const popoverRect = popover.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      
      // Check horizontal overflow
      const wouldOverflowRight = triggerRect.left + popoverRect.width > viewportWidth;
      const wouldOverflowLeft = triggerRect.left < 0;
      
      // Check vertical overflow
      const wouldOverflowBottom = triggerRect.bottom + popoverRect.height > viewportHeight;
      
      if (wouldOverflowRight) {
        this.popoverAlign = 'right';
      } else if (wouldOverflowLeft) {
        this.popoverAlign = 'left';
      } else {
        this.popoverAlign = 'left';
      }
      
      // Force change detection to apply the new alignment
      this.cdr.detectChanges();
    }
  }

  get orgLabel(): string {
    // Try to get the org name from the org_path cookie
    const orgPath = this.tokenStorage.getCookie('org_path');
    if (orgPath) {
      const orgName = orgPath.split('::')[0].split('@')[0];
      if (orgName) return orgName;
    }
    // Fallback to dropdown label
    return this.orgDropdownOptions.find(o => o.value === this.selectedOrg)?.label || 'Select Organization';
  }
  get teamLabel(): string {
    return this.teamDropdownOptions.find(t => t.value === this.selectedTeam)?.label || '';
  }

  get orgControl() {
    return this.headerConfigForm.get('org') as FormControl;
  }
  get domainControl() {
    return this.headerConfigForm.get('domain') as FormControl;
  }
  get projectControl() {
    return this.headerConfigForm.get('project') as FormControl;
  }
  get teamControl() {
    return this.headerConfigForm.get('team') as FormControl;
  }

  saveOrgPathAndClose() {
    // Get existing path from cookie to preserve unchanged values
    const existingPath = this.tokenStorage.getCookie('org_path');
    let existingLabelsPath = '';
    let existingIdsPath = '';
    
    if (existingPath) {
      const parts = existingPath.split('::');
      existingLabelsPath = parts[0] || '';
      existingIdsPath = parts[1] || '';
    }
    
    // Get current form values (IDs)
    const currentFormValues = this.headerConfigForm.value;
    const newIdsPath = Object.values(currentFormValues).join('@');
    
    // Build labels path by preserving existing values and updating changed ones
    const existingLabels = existingLabelsPath ? existingLabelsPath.split('@') : ['', '', '', ''];
    const currentLabels = Object.values(this.selectedDropdownValues);
    
    // Use current labels where available, otherwise fall back to existing labels
    const finalLabels = currentLabels.map((label, index) => {
      return label.trim() !== '' ? label : (existingLabels[index] || '');
    });
    
    const newLabelsPath = finalLabels.filter(label => label.trim() !== '').join('@');
    const newPath = `${newLabelsPath}::${newIdsPath}`;
    
    if (newPath) {
      // Delete existing cookie and set new one for clean update
      this.tokenStorage.deleteCookie('org_path');
      this.tokenStorage.setCookie('org_path', newPath);
      this.closeOrgDialog();
      this.router.navigate(['/dashboard']);
    }
  }

  closeOrgDialog() {
    this.isOrgDialogOpen = false;
  }
}
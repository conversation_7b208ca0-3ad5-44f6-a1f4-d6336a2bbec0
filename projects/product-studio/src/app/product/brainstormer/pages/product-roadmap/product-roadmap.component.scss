.roadmap-container {
  // padding: 1.25rem;
}

h1 {
  color: #1f2937;
  margin-bottom: 20px;
  font-size: 24px;
}

button {
  background: none;
  border: none;
  color: #1f2937;
  font-size: 14px;
  cursor: pointer;
}

.controls {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.btn-toggle {
  cursor: pointer;
  border: none;
  background: transparent;
  transition: background 0.3s;

  &:hover {
    background: #d3dcee;
    border: 1px solid #d7e2ff;
    border-radius: 12px;
  }

  &.active {
    background: #c4d1eb;
    border: 1px solid #d7e2ff;
    border-radius: 12px;
  }
}
.roadmap-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 65px;

  .left-section {
    flex: 0 0 auto; // Don't grow/shrink
  }

  .nav-btn {
    flex: 0 0 auto; // Don't grow/shrink

    .nav-arrow {
      width: 32px;
      height: 32px;
      border: 1px solid #e5e7eb;
      border-radius: 4px;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      &:hover {
        background: #f9fafb;
      }
    }
  }

  .right-container {
    flex: 0 0 auto; // Don't grow/shrink

    .toggle-view {
      cursor: pointer;
      border-radius: 12px;
      background: #fff;
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
      height: 55px;
    }
  }
  .add-task-btn {
    border-radius: 8px;
    background: #7d63f6;
    display: inline-flex;
    padding: 12px 24px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    &:hover {
      background-color: #5b3aeb;
    }

    button {
      background: none;
      border: none;
      padding: 0;
      color: white;
      cursor: pointer;
      min-height: 0;
      min-width: 0;
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .roadmap-nav {
      flex-wrap: wrap;
      gap: 12px;

      .left-section {
        flex: 1 1 100%;
        justify-content: space-between;
      }

      .nav-btn,
      .right-container {
        flex: 1 1 auto;
      }
    }
  }
}

:host ::ng-deep .dropdown {
  height: none;
}

:host ::ng-deep .calendar-input {
  height: 48px !important;
}

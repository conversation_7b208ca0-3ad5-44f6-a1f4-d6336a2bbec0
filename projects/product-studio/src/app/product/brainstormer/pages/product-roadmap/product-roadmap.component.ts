import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import {
  GanttChartComponent,
  GanttTask,
} from '../../components/gantt-chart/gantt-chart.component';
import { DatepickerComponent, DropdownComponent, IconsComponent } from '@awe/play-comp-library';
import { ProductRoadmapCardViewComponent } from "../product-roadmap-card-view/product-roadmap-card-view.component";
import { RoadmapDataService, RoadmapTask, Priority } from '../../services/roadmap-data.service';
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';

@Component({
  selector: 'app-product-roadmap',
  standalone: true,
  imports: [
    GanttChartComponent,
    CommonModule,
    FormsModule,
    IconsComponent,
    DropdownComponent,
    DatepickerComponent,
    ProductRoadmapCardViewComponent,
    AweModalComponent
  ],
  templateUrl: './product-roadmap.component.html',
  styleUrl: './product-roadmap.component.scss',
})
export class ProductRoadmapComponent implements OnInit, OnDestroy {
  chartView: string = '/assets/icons/chart-view.png';
  cardView: string = 'assets/icons/card-view.png';
  activeView: 'timeline' | 'card' = 'timeline'; // Changed to represent view types

  projectTasks: GanttTask[] = [];
  private subscription = new Subscription();

  // Modal State
  isEditModalOpen = false;
  selectedTaskForEdit: RoadmapTask | null = null;
  isAddingNewTask = false;

  // Working copy properties for editing
  editableTaskTitle: string = '';
  editableTaskDescription: string = '';
  editableTaskPriority: Priority = 'medium';
  editableTaskStartDate: string = '';
  editableTaskEndDate: string = '';
  regeneratePrompt: string = '';

  constructor(private roadmapDataService: RoadmapDataService) {}

  ngOnInit(): void {
    // Subscribe to tasks and convert to GanttTask format
    this.subscription.add(
      this.roadmapDataService.tasks$.subscribe(() => {
        this.projectTasks = this.roadmapDataService.getGanttTasks();
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  // Modal Methods
  addNewTask(): void {
    this.isAddingNewTask = true;
    this.selectedTaskForEdit = null;
    this.clearEditableData();
    this.isEditModalOpen = true;
  }

  openEditModal(task: RoadmapTask): void {
    this.isAddingNewTask = false;
    this.selectedTaskForEdit = { ...task };
    this.setupEditableData(task);
    this.isEditModalOpen = true;
  }

  closeEditModal(): void {
    this.isEditModalOpen = false;
    this.selectedTaskForEdit = null;
    this.isAddingNewTask = false;
    this.clearEditableData();
  }

  updateTask(): void {
    if (this.isAddingNewTask) {
      // Add new task
      const startDate = this.roadmapDataService.parseDateFromInput(this.editableTaskStartDate);
      const endDate = this.roadmapDataService.parseDateFromInput(this.editableTaskEndDate);
      const quarter = this.roadmapDataService.getQuarterFromDate(startDate);

      this.roadmapDataService.addTask({
        task: this.editableTaskTitle,
        description: this.editableTaskDescription,
        priority: this.editableTaskPriority,
        startDate: startDate,
        endDate: endDate,
        quarter: quarter,
      });
    } else if (this.selectedTaskForEdit) {
      // Update existing task
      const startDate = this.roadmapDataService.parseDateFromInput(this.editableTaskStartDate);
      const endDate = this.roadmapDataService.parseDateFromInput(this.editableTaskEndDate);
      const quarter = this.roadmapDataService.getQuarterFromDate(startDate);

      this.roadmapDataService.updateTask(this.selectedTaskForEdit.id, {
        task: this.editableTaskTitle,
        description: this.editableTaskDescription,
        priority: this.editableTaskPriority,
        startDate: startDate,
        endDate: endDate,
        quarter: quarter,
      });
    }
    this.closeEditModal();
  }

  deleteTask(taskId: string): void {
    this.roadmapDataService.deleteTask(taskId);
  }

  private clearEditableData(): void {
    this.editableTaskTitle = '';
    this.editableTaskDescription = '';
    this.editableTaskPriority = 'medium';
    this.editableTaskStartDate = '';
    this.editableTaskEndDate = '';
    this.regeneratePrompt = '';
  }

  private setupEditableData(task: RoadmapTask): void {
    this.editableTaskTitle = task.task;
    this.editableTaskDescription = task.description;
    this.editableTaskPriority = task.priority;
    this.editableTaskStartDate = this.roadmapDataService.formatDateForInput(task.startDate);
    this.editableTaskEndDate = this.roadmapDataService.formatDateForInput(task.endDate);
    this.regeneratePrompt = '';
  }

  onRangeSelected(event: { start: Date, end: Date }) {
    console.log('Selected Range:', event.start, event.end);
    // Handle the selected range as needed
  }

  setActiveView(view: 'timeline' | 'card') {
    this.activeView = view;
  }
}



:host {
  display: block;
}

// Page Layout & Header
.page-container {
  background: rgba(101, 102, 205, 0.12);
  box-shadow:
    0px 4px 8px -2px rgba(16, 24, 40, 0.1),
    0px 2px 4px -2px rgba(16, 24, 40, 0.06);
}

.top-header-gradient {
  height: 0.5rem;
  background: linear-gradient(90deg, #8a2be2, #4a00e0);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.avatar-sm {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}
.avatar-md {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

// Left Summary Card
.persona-summary-card {
  position: relative; // Needed for absolute positioning of the edit button
  border-radius: 12px;
  border: none;
  border-radius: 12px;
  border: 1px solid var(--LightMode-Container-Stroke, #e4e7ec);
  background: var(--LightMode-Container, #fff);
  .card-body {
    padding: 1rem;

    /* Where are you? */
    box-shadow:
      0px 4px 8px -2px rgba(16, 24, 40, 0.1),
      0px 2px 4px -2px rgba(16, 24, 40, 0.06);
  }
}

.dropdown {
  position: relative;
  // height: 44px;

  .dropdown-menu {
    display: none;
    position: absolute;
    top: 35%;
    right: 70%;
    min-width: 120px;
    background-color: #fff;
    // border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    // padding: 0.5rem 0;
    z-index: 1000;

    &.show {
      display: block;
    }

    .dropdown-item {
      display: block;
      width: 100%;
      // border-bottom: 1px solid #303233;
      // padding: 0.375rem 1rem;
      clear: both;
      font-weight: 400;
      color: #212529;
      text-align: inherit;
      text-decoration: none;
      white-space: nowrap;
      background-color: transparent;
      // border: 0;
      transition: background-color 0.15s ease-in-out;
      cursor: pointer;

      &:hover,
      &:focus {
        background-color: #f8f9fa;
        color: #1e2125;
      }

      &.text-danger:hover {
        background-color: #f5c2c7;
        color: #842029;
      }
    }
  }
}

button {
  min-width: 0px;
  border: none;
  background: none;
}
// .three-dot-icon {
//   margin: 1.25rem;
//   cursor: pointer !important;
// }

// **** STYLE FOR THE RESTORED BUTTON ****
.btn-edit-main {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  z-index: 2;
  background: transparent;
  border: none;
  padding: 0.25rem;
  line-height: 1;

  img {
    width: 16px;
    height: 16px;
    opacity: 0.6;
    transition: opacity 0.2s ease-in-out;
  }

  &:hover img {
    opacity: 1;
  }
}

.profile-section {
  text-align: center;
  margin-bottom: 0.25rem;
  .avatar-wrapper {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    padding: 5px;
    background: linear-gradient(135deg, #fbcfe8, #9b59b6);
  }
  .avatar-img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
  }
  .role-title {
    color: #4a00e0;
    font-size: 1.5rem;
    font-weight: bold;
  }
}

.info-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f5f5fa;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    span:first-child {
      color: #6c757d;
    }
    span:last-child {
      font-weight: 500;
    }
  }
}

.quote-section {
  padding: 6px 1rem 1rem;
  border-radius: 8px;
  position: relative;
  background-color: #f5f5fa;
  // margin-bottom: 1.5rem;
  .quote-icon {
    position: absolute;
    top: 1rem;
    left: 1rem;
    opacity: 0.6;
  }
  .quote-text {
    padding-left: 2rem;
    margin: 0;
    color: #6c757d;
    font-style: italic;
  }
}

.personality-section {
  .personality-title {
    font-size: 0.9rem;
    font-weight: bold;
    padding: 0.5rem;
  }
  .personality-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    .tag {
      padding: 0.3rem 0.8rem;
      border-radius: 16px;
      font-size: 0.8rem;
      &:nth-child(3n + 1) {
        background-color: #e0e7ff;
        color: #4338ca;
      }
      &:nth-child(3n + 2) {
        background-color: #e0e7ff;
        color: #4338ca;
      }
      &:nth-child(3n + 3) {
        background-color: #e0e7ff;
        color: #4338ca;
      }
    }
  }
}

// Right Detail Cards
.detail-card {
  border: none;
  border-radius: 12px;
  border: 1px solid var(--LightMode-Container-Stroke, #e4e7ec);
  background: var(--LightMode-Container, #fff);
  box-shadow:
    0px 4px 8px -2px rgba(16, 24, 40, 0.1),
    0px 2px 4px -2px rgba(16, 24, 40, 0.06);
  .card-header {
    background: none;
    border-bottom: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 1.5rem 0.5rem;
    h3 {
      font-size: 1.1rem;
      font-weight: bold;
      margin: 0;
    }
  }
  .btn-edit {
    background: transparent;
    border: none;
    padding: 0.25rem;
    img {
      width: 16px;
      height: 16px;
      opacity: 0.6;
      transition: opacity 0.2s;
    }
    &:hover img {
      opacity: 1;
    }
  }
  .card-body {
    padding: 0.5rem 1.5rem 1.5rem;
    ul {
      list-style: none;
      padding-left: 0;
      margin: 0;
    }
    li {
      position: relative;
      padding-left: 1.25rem;
      margin-bottom: 0.5rem;
    }
    li::before {
      content: "•";
      position: absolute;
      left: 0;
      color: #4a00e0;
      font-weight: bold;
    }
  }
}

.device-height {
  min-height: 231px;
  height: 231px;
}

.device-body {
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 1.5rem;
  min-height: 120px;
  img {
    width: 65px;
    height: 65px;
    filter: grayscale(30%) opacity(0.8);
  }
}

// Progress Bar Gradients
.progress {
  background-color: #e9ecef;
  border-radius: 25px;
  width: 75%;
}
.progress-bar {
  &.bg-primary {
    background: linear-gradient(90deg, #8a2be2, #4a00e0);
  }
  &.bg-info {
    background: linear-gradient(90deg, #8a2be2, #4a00e0);
  }
  &.bg-success {
    background: linear-gradient(990deg, #8a2be2, #4a00e0);
  }
}

// Persona Selector Dropdown
.persona-selector-card {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  width: 300px;
  padding-top: 8px;
  .list-group-item-action:hover {
    background-color: #f0e6ff;
  }
}

// Dialog Panel Style
::ng-deep
  .custom-dialog-container-no-padding
  .mat-mdc-dialog-container
  .mdc-dialog__surface {
  padding: 0 !important;
  border-radius: 1rem !important;
}

.profile-data {
  :host ::ng-deep .input-container {
    display: flex;
    width: 100%;
    flex-direction: row;
    align-items: center;
    gap: 5px;
    height: auto;
    padding: 0;
  }
  :host ::ng-deep .input-container label {
    width: 20%;
    // align-self: flex-start;
    align-items: center;
    font-size: 1rem;
    flex-grow: 0;
    display: none;
  }

  :host ::ng-deep.input-wrapper {
    width: 80%;
    // flex-grow: 1;
  }

  :host ::ng-deep .input-container .input-wrapper {
    width: 80%;
    flex-grow: 1;
  }
}
:host ::ng-deep .input-container label {
  display: none;
}

:host ::ng-deep .input-container {
  padding: 0;
}
.persona-filter{
  position: relative;
  margin-left: auto;
  margin-right: 1rem;
  top: 10px;
  right: 0px;
}

.add-new {
  border: 2px solid #858aad;
  border-radius: 0.5rem;
  height: 60px;
  background: none;
  font-size: 1rem;
  cursor: pointer;
  width: 100%;
}

:host ::ng-deep awe-heading {
  margin-bottom: 0.5rem;
}

:host ::ng-deep .input-container .input-wrapper.expanded {
  height: 125px;
}

button {
  border-radius: 50px;
  background: #fff;
  padding: 12px 24px;
  border: none;
  border-radius: 50px;
}
.btn-cancel {
  border: 1px solid var(--Primary-500, #7c3aed);
  background: var(--Neutral-Neutral-colors-Solid, #fff);
}
.btn-delete {
  background: var(--Primary-500, #7c3aed);
  border: 1px solid var(--Primary-500, #7c3aed);
  color: #fff;
}

.inp-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  // align-items: stretch;
  gap: 10px;
  margin-bottom: 0;
  .label {
    width: auto;
    display: flex;
    align-self: center;
    align-items: center;
    font-size: 1rem;
    flex-grow: 0;
  }
  .input-wrapper {
    width: auto;
    flex-grow: 1;
  }
}

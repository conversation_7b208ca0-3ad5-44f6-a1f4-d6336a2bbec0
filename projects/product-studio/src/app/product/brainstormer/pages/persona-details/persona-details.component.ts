import { Component, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';

import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';
import { PersonaDataService, PersonaData, PersonaCard } from '../../services/persona-data.service';
import { HeadingComponent, IconsComponent, InputComponent, SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-persona-details',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AweModalComponent,
    HeadingComponent,
    IconsComponent,
    InputComponent,
    SliderComponent
  ],
  templateUrl: './persona-details.component.html',
  styleUrls: ['./persona-details.component.scss']
})
export class PersonaDetailsComponent implements OnInit, OnDestroy {
  // Output events
  @Output() backToList = new EventEmitter<void>();

  // Icons
  PencilEditIcon: string = '/icons/pencil-edit.svg';
  MobileIcon: string = '/mobile-icon/mobile-icon-light.svg';
  LaptopIcon: string = '/web-icon/web-icon-light.svg';
  colon: string = '/svgs/colon.svg';
  InlargeIcon: string = '/svgs/inlarge-icon.svg';
  threeDotsIcon: string = '/icons/three-dot.svg';

  // State
  public isPersonaSelectorOpen = false;

  // Modal state
  isEditModalOpen = false;
  selectedCardForEdit: PersonaCard | null = null;
  editData: any = {};
  regeneratePrompt = '';

  // Dropdown state
  openDropdownId: string | null = null;

  // Data
  selectedPersona: PersonaData | null = null;
  personas: PersonaData[] = [];
  private subscription = new Subscription();

  // Time period options
  timePeriodOptions: { name: string; value: string }[] = [
    { name: 'Quarter wise', value: 'quarter-wise' },
    { name: 'Month wise', value: 'month-wise' },
    { name: 'Day wise', value: 'day-wise' },
  ];

  constructor(
    private personaDataService: PersonaDataService
  ) {}

  ngOnInit(): void {
    // Subscribe to personas data
    this.subscription.add(
      this.personaDataService.personas$.subscribe(personas => {
        this.personas = personas;
      })
    );

    // Subscribe to selected persona
    this.subscription.add(
      this.personaDataService.selectedPersona$.subscribe(persona => {
        this.selectedPersona = persona;
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  
  // --- Dropdown Methods ---
  toggleDropdown(cardId: string): void {
    this.openDropdownId = this.openDropdownId === cardId ? null : cardId;
  }

  closeAllDropdowns(): void {
    this.openDropdownId = null;
  }

  isDropdownOpen(cardId: string): boolean {
    return this.openDropdownId === cardId;
  }

  // --- Modal Methods ---
  openEditModal(cardType: PersonaCard['type'] | 'profile', title: string): void {
    if (!this.selectedPersona) return;

    this.selectedCardForEdit = {
      id: `${this.selectedPersona.id}-${cardType}`,
      title,
      type: cardType,
      data: cardType === 'profile' ? this.selectedPersona : (this.selectedPersona as any)[cardType]
    };

    // Handle different data types
    if (cardType === 'profile') {
      // For profile, we need to create an object with all profile-related fields
      this.editData = {
        name: this.selectedPersona.name || '',
        role: this.selectedPersona.role || '',
        age: this.selectedPersona.age || '',
        education: this.selectedPersona.education || '',
        status: this.selectedPersona.status || '',
        location: this.selectedPersona.location || '',
        techLiteracy: this.selectedPersona.techLiteracy || '',
        quote: this.selectedPersona.quote || '',
        avatar: this.selectedPersona.avatar || '',
        personality: this.selectedPersona.personality ? [...this.selectedPersona.personality] : []
      };
    } else if ((cardType as string) !== 'profile' && Array.isArray((this.selectedPersona as any)[cardType])) {
      // For arrays, create a deep copy
      this.editData = [...(this.selectedPersona as any)[cardType]];
    } else if ((cardType as string) !== 'profile') {
      // For other types, create a copy
      this.editData = (this.selectedPersona as any)[cardType];
    }

    this.isEditModalOpen = true;
    this.closeAllDropdowns();
  }

  closeEditModal(): void {
    this.isEditModalOpen = false;
    this.selectedCardForEdit = null;
    this.editData = {};
    this.regeneratePrompt = '';
  }

  saveCardData(): void {
    if (!this.selectedCardForEdit || !this.selectedPersona) return;

    if (this.selectedCardForEdit.type === 'profile') {
      // For profile, update multiple fields
      this.personaDataService.updatePersonaProfile(this.selectedPersona.id, this.editData);
    } else {
      // For other card types, update the specific property
      this.personaDataService.updatePersonaCard(
        this.selectedPersona.id,
        this.selectedCardForEdit.type,
        this.editData
      );
    }

    this.closeEditModal();
  }

  // --- Device Toggle Method ---
  toggleDevice(deviceType: string): void {
    if (!Array.isArray(this.editData)) {
      this.editData = [];
    }

    const index = this.editData.indexOf(deviceType);
    if (index > -1) {
      // Remove device if it exists
      this.editData.splice(index, 1);
    } else {
      // Add device if it doesn't exist
      this.editData.push(deviceType);
    }
  }

  // --- Regenerate Method ---
  onRegenerate(): void {
    if (!this.regeneratePrompt.trim()) {
      console.log('Regenerate prompt is empty.');
      return;
    }

    console.log('Regenerating with prompt:', this.regeneratePrompt);
    // Here you would typically call your AI service to regenerate content
    // For now, we'll just log the prompt
    this.regeneratePrompt = '';
  }

  // --- Navigation Methods ---
  togglePersonaSelector(): void {
    this.isPersonaSelectorOpen = !this.isPersonaSelectorOpen;
  }

  selectPersona(personaId: string): void {
    this.personaDataService.setSelectedPersona(personaId);
    this.isPersonaSelectorOpen = false;
  }

  goBack(): void {
    this.backToList.emit();
  }

  // --- Utility Methods ---
  getCardTitle(type: PersonaCard['type'] | 'profile'): string {
    const titles = {
      painPoints: 'Pain Points',
      goals: 'Goals',
      motivation: 'Motivation',
      expectations: 'Expectations',
      skills: 'Skills',
      devices: 'Devices',
      profile: 'Profile'
    };
    return titles[type] || type.charAt(0).toUpperCase() + type.slice(1);
  }

  // --- Profile Methods ---
  getProfileFields(): { label: string, value: string }[] {
    if (!this.selectedPersona) return [];

    return [
      { label: 'Name', value: this.selectedPersona.name || '' },
      { label: 'Role', value: this.selectedPersona.role || '' },
      { label: 'Age', value: this.selectedPersona.age?.toString() || '' },
      { label: 'Education', value: this.selectedPersona.education || '' },
      { label: 'Status', value: this.selectedPersona.status || '' },
      { label: 'Location', value: this.selectedPersona.location || '' },
      { label: 'Tech Literacy', value: this.selectedPersona.techLiteracy || '' },
      { label: 'Quote', value: this.selectedPersona.quote || '' }
    ];
  }

  getPersonalityTraits(): string[] {
    return this.selectedPersona?.personality || [];
  }

  // --- Card Data Methods ---
  getCardData(cardType: PersonaCard['type']): any {
    if (!this.selectedPersona) return null;

    return cardType === 'profile' ? this.selectedPersona : (this.selectedPersona as any)[cardType];
  }

  // --- Avatar Methods ---
  getAvatarUrl(): string {
    return this.selectedPersona?.avatar || '';
  }

  // --- Device Methods ---
  isDeviceSelected(deviceType: string): boolean {
    return Array.isArray(this.editData) && this.editData.includes(deviceType);
  }

  // --- Form Validation ---
  isFormValid(): boolean {
    if (!this.selectedCardForEdit) return false;

    if (this.selectedCardForEdit.type === 'profile') {
      return !!(this.editData.name && this.editData.role);
    }

    if (this.selectedCardForEdit.type === 'skills') {
      return Array.isArray(this.editData) &&
             this.editData.length > 0 &&
             this.editData.every((skill: any) => skill.name && skill.level);
    }

    if (Array.isArray(this.editData)) {
      return this.editData.length > 0 && this.editData.every(item => item && item.trim());
    }

    return true;
  }

  // --- Array Data Helper Methods ---
  isArrayData(data: any): boolean {
    return Array.isArray(data);
  }

  trackByIndex(index: number, _item: any): number {
    return index;
  }

  addArrayItem(): void {
    if (!this.selectedCardForEdit) return;

    if (this.selectedCardForEdit.type === 'skills') {
      if (!Array.isArray(this.editData)) {
        this.editData = [];
      }
      this.editData.push({ name: '', level: 0 });
    } else {
      if (!Array.isArray(this.editData)) {
        this.editData = [];
      }
      this.editData.push('');
    }
  }

  removeArrayItem(index: number): void {
    if (Array.isArray(this.editData) && index >= 0 && index < this.editData.length) {
      this.editData.splice(index, 1);
    }
  }

  // --- Progress Bar Helper ---
  getProgressBarClass(index: number): string {
    const colors = ['bg-primary', 'bg-success', 'bg-info', 'bg-warning', 'bg-danger'];
    return colors[index % colors.length];
  }

  // --- Drag and Drop Methods ---
  onDragStart(index: number): void {
    // Store the dragged item index for future drag/drop functionality
    console.log('Drag start:', index);
  }

  onDragEnd(index: number): void {
    // Handle drag end for future drag/drop functionality
    console.log('Drag end:', index);
  }
}
// Minimal custom SCSS - most styling handled by Bootstrap classes
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@900&display=swap");

// Letter spacing utility (Bootstrap doesn't have this)
.ls-1 {
  letter-spacing: 1px;
}
// Section Headers
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: auto;
  background: var(--swot-card-header-bg) !important;
  border-radius: 12px 12px 0px 0px;
  color: var(--swot-card-header-title-color);

  .section-title {
    font-size: 28px;
    font-weight: 700;
    letter-spacing: 1px;
  }
  .section-action {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--swot-card-header-action-bg);
    border-radius: 16px;
    color: var(--swot-card-header-action-color);
    padding: 16px;
    font-size: 24px;
    font-weight: 700;
    line-height: 1;
  }
}

.feature-list {
  background-color: var(--feature-card-bg);
  min-height: 655px;
    cursor: grab !important;
    &:active {
    cursor: grabbing !important;
  }
}

.three-dot-icon {
  margin: 1.25rem;
  cursor: pointer !important;
}

:host ::ng-deep .feature-title {
  color: var(--feature-card-title);
}
.add-more-section {
  .add-more-btn {
    background-color: var(--feature-cared-add-card-btn-bg);
    border: 1px solid var(--feature-cared-add-card-btn-border);
    border-radius: 12px;
    color: var(--feature-cared-add-card-btn-color);
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    padding: 0.75rem 1rem;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer !important;


    .plus-icon {
      font-size: 20px;
      margin-left: 0.5rem;
    }
  }
}
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 0.5rem;
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.15),
    0 4px 10px rgba(0, 0, 0, 0.1);
  transform: rotate(2deg);
 
}

.cdk-drag-placeholder {
  opacity: 0.3;
  background: rgba(108, 117, 125, 0.1);
  border: 2px dashed #6c757d;
  border-radius: 0.5rem;
  transition: all 0.3s ease;

}

.cdk-drag-animating {
  transition: transform 350ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

:host ::ng-deep awe-card.cdk-drag-dragging {
  cursor: grabbing !important;
}

Card hover effects
.feature-card {
  min-height: 236px;
  cursor: grabbing !important;
  &:active {
    cursor: grabbing !important;
  }
}

button {
  min-width: 0px;
  border: none;
  background: none;
}

.border-buttom {
  border-bottom: 1px solid #303233;
}

// Custom dropdown styling
.dropdown {
  position: relative;
  height: 44px;

  .dropdown-menu {
    display: none;
    position: absolute;
    top: 35%;
    right: 70%;
    min-width: 120px;
    background-color: #fff;
    // border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    // padding: 0.5rem 0;
    z-index: 1000;

    &.show {
      display: block;
    }

    .dropdown-item {
      display: block;
      width: 100%;
      // border-bottom: 1px solid #303233;
      // padding: 0.375rem 1rem;
      clear: both;
      font-weight: 400;
      color: #1c1d1e;
      text-align: inherit;
      text-decoration: none;
      white-space: nowrap;
      background-color: transparent;
      // border: 0;
      transition: background-color 0.15s ease-in-out;
      cursor: pointer;

      &:hover,
      &:focus {
        background-color: #f8f9fa;
        color: #1e2125;
      }

      &.text-danger:hover {
        background-color: #f5c2c7;
        color: #842029;
      }
    }
  }
}
// Progress bar styling
.progress {
  background-color: #e9ecef;
  border-radius: 3px;
  height: 8px;

  .progress-bar {
    
    border-radius: 3px;
    transition: width 0.3s ease;
  }
}



:host ::ng-deep .input-container label {
  display: none;
}
:host ::ng-deep .input-container .input-wrapper.expanded {
  height: 125px;
}
:host ::ng-deep .input-container {
  padding: 0;
}
.add-new {
  border: 2px solid #858aad;
  border-radius: 0.5rem;
  height: 60px;
  background: none;
  font-size: 1rem;
  cursor: pointer;
  width: 100%;
  &:hover {
    background-color: #e9ecef;
  }
}

 button {
    border-radius: 50px;
    background: #fff;
    padding: 12px 24px;
    border: none;
    border-radius: 50px;
  }
.btn-cancel {
  border: 1px solid var(--Primary-500, #7c3aed);
  background: var(--Neutral-Neutral-colors-Solid, #fff);
}
.btn-delete {
  background: var(--Primary-500, #7c3aed);
  border: 1px solid var(--Primary-500, #7c3aed);
  color: #fff;
}